---
weight: 3
title: "Resource Tagging"
date: 2025-16-06
tags: ["multi cloud", "naming convention", "", ""]
summary: "Cloud Naming Convention"
---

## Naming Convention Overview

**Name**: Resource Naming in Multi-Cloud
**Description**: Resource naming in multi-cloud environments is a critical component of consistent resource management across Azure, AWS, GCP or Alibaba. By standardizing naming conventions, resources such as virtual machines, storage accounts, and databases can be efficiently tracked, managed, and reported regardless of their hosting platform.

### Lifecycle
  - Published Date: 2025-22-04 

## Goals & Benefits
  - Provide network segmentation and packet inspection when integrating with 3rd party data services hosted outside of PepsiCo network.
  - Network connections to 3rd party data services are always initiated from PepsiCo side, data then will be pushed / pulled over the established connections.
  - 3rd party data services are not allowed to initiate network connections to PepsiCo network
  
## Naming Convention Guidelines

### Guideline


### Limitations
  - TBD

### Example
  - TBD

### Sample Use Cases
  - Initiate network connection to 3rd Party data services to pull or push data.
  - For example, ADF SHIR, PBI Gateway, IRI integration.
  
### Additional Guardrails
  - TBD
  
## Technical Guidelines
  - TBD

### Networking & Security Architecture Diagram
![Architecture Diagram](/images/datahub_isolated_architecture.png)

## Related Patterns
  - Enterprise Firewall
  - Isolated DMZ Firewall
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 6 June 2025 (Sakshi Sharma)

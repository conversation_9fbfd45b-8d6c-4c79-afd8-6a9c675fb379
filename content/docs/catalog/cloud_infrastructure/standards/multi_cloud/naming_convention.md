---
weight: 2
title: "Cloud Naming Convention"
date: 2025-22-06
tags: ["multi cloud", "naming convention", "", ""]
summary: "Cloud Naming Convention"
---

## Naming Convention Overview

**Name**: Resource Naming in Multi-Cloud
**Description**: Resource naming in multi-cloud environments is a critical component of consistent resource management across Azure, AWS, GCP or Alibaba. By standardizing naming conventions, resources such as virtual machines, storage accounts, and databases can be efficiently tracked, managed, and reported regardless of their hosting platform.

### Lifecycle
  - Published Date: 2025-22-04 

## Goals & Benefits
  - Standardized names simplify cross-cloud management and reporting.
  - Enables scripting, automated tagging, and policy enforcement with predictable resource names.
  - Clear naming reduces risks of resource misconfiguration or misidentification.
  - Supports large scale environments with environment, region, and sequence differentiation.
  - Enforces adherence to cloud provider naming restrictions and internal policies.
  
## Naming Convention Guidelines

### Guideline

| Guideline                                 | Azure | AWS | GCP | Alibaba |
|-------------------------------------------|:-----:|:---:|:---:|:-------:|
| Lower case only                           | ✅    | ✅  | ✅  | ✅      |
| Dashes (-) for separation                 | ✅    | ✅  | ✅  | ✅      |
| Regex: pep[a-z0-9(-)], cannot start or end with hyphen | ✅ | ✅ | ✅ | ✅  |
| `<serviceName>` as application identifier | ✅    | ✅  | ✅  | ✅      |
| Remove spaces in service/component names  | ✅    | ✅  | ✅  | ✅      |
| Cloud Vendor values                       | azr   | aws | gpc | ali     |
| Environment values                        | sbx, dev, test, qa, preprd, prod, dr, global (same for all clouds) |     |     |         |
| Region values                             | scus, eus, suk, gwc, sea, ae (Azure only; TBD for others) |     |     |         |
| Maximum characters                        | 63    | 63  | 63  | 63      |

- *Currently less than 1% of Azure resources have names with more than 63 characters 

### General Naming Format
    pep-<cloudVendor>-<serviceName>-<componentOrFunction>-<environment>-<region>-<sequenceNumber>-<resourceTypeAcronym
  - pep: Prefix identifying the organization (PepsiCo)
  - cloudVendor: Short cloud provider code (e.g., azr, aws, gpc, ali)
  - serviceName: Application or service name (no spaces)
  - componentOrFunction: Optional component or function name
  - environment: Environment code (e.g., dev, test, prod)
  - region: Cloud region code
  - sequenceNumber: Numeric sequence to differentiate instances
  - resourceTypeAcronym: Short acronym identifying resource type

### Limitations
  - Regional codes for AWS, GCP, and Alibaba are TBD and may vary, so care must be taken to update and standardize them once defined.
  - Certain resource types (e.g., storage accounts) disallow dashes, requiring a concatenated naming approach that may reduce readability.
  - Sequence numbers must be carefully managed to avoid naming collisions in large environments.
  - Naming length is capped at 63 characters, limiting descriptive detail for deeply nested naming.

### Cloud Naming Convention: Limitations and Particularities

This table outlines the naming convention limitations and particularities across **Azure**, **AWS**, and **GCP** in alignment with PepsiCo internal standards.

| Limitations and Particularities                                                                                       | Azure | AWS | GCP |
|------------------------------------------------------------------------------------------------------------------------|:-----:|:---:|:---:|
| Leverage the current PepsiCo Server Naming Convention for Virtual Machines/EC2 (Internal link)                        | ✅    | ✅  | ✅  |
| Server name is generated by an internal VM Name Generator Tool (DND) based on SNOW request                            | ✅    | ✅  | ✅  |
| Underscore not allowed; spaces forbidden; periods forbidden                                                            | ✅    | ✅  | ✅  |
| `<component/function>` can be removed if resource is global for one application                                       | ✅    | ✅  | ✅  |
| `<component/function>` can be replaced by `<sector>` if resource is sector-level                                      | ✅    | ✅  | ✅  |
| If `<service name>/<component/function>` has a space (e.g., “Data Hub”), remove the space (e.g., “datahub”)           | ✅    | ✅  | ✅  |
| Resources that cannot have dashes in naming use the same format without dashes (e.g., storage accounts, SQL pools)    | ✅    | ✅  | ✅  |
| Use “connected-to” for resources involving datacenter connectivity (e.g., ExpressRoute, route filters, connections)   | ✅    | ✅  | ✅  |
| Network resources must reference connected resources (e.g., VNET, Subnet, internal/external)                          | ✅    | ✅  | ✅  |
| Resources dependent on others integrate the parent resource name (e.g., `pepvmname01-os-disk-dev-scus-01`)            | ✅    | ✅  | ✅  |


## Examples

  ### Naming Conventions Examples

### Azure Resource Naming Convention Catalog

| **Resource Type**                  | **Resource Acronym** | **Naming Convention**                                                                 | **Example**                                           |
|-----------------------------------|----------------------|----------------------------------------------------------------------------------------|-------------------------------------------------------|
| OS Disk                           | os-disk              | `<vm-name>-<resource acronym>-<environment>-<region>-<sequence number>`               | pepvmname01-os-disk-dev-scus-01                      |
| Virtual Machine                   | vm                   | PepsiCo Compute Standards                                                             | pepvmname01                                           |
| Data Disk                         | data-disk            | `<vm-name>-<resource acronym>-<environment>-<region>-<sequence number>`               | pepvmname01-data-dev-scus-disk-01                    |
| Network Interface                 | nic                  | `<vm-name>-<resource acronym>-<environment>-<region>-<sequence number>`               | pepvmname01-nic-dev-scus-01                          |
| Private Endpoint                  | pe                   | `pep-<service name>-<connected resource>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-datahub-blob-dev-scus-01-pe              |
| Storage Account                   | sa                   | `pep<service name><environment><region><sequence number><resource acronym>`           | pepdatahubdevscus01sa                                |
| Application Security Group        | asg                  | `pep-<service name>-<component/function>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-sap-frontend-dev-scus-01-asg            |
| Availability Set                  | aset                 | Same as above                                                                         | pep-datahub-frontend-dev-scus-01-aset                |
| Network Security Group            | nsg                  | `pep-<service name>-<environment>-<region>-<scope>-<sequence number>-<resource acronym>` | pep-paloalto-dev-scus-subnet-01-nsg         |
| NetApp Volume                     | vol                  | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus01-vol                  |
| Metric Alert Rule                 | mar                  | `<metric monitor>-<resource name>-<resource acronym>`                                 | cpupepvmname01-mar                                    |
| Key Vault                         | kv                   | `pep-<service name/application short name>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-cmp-dev-scus-01-kv               |
| Smart Detector Alert Rule         | sdar                 | `Failure Anomalies <Application Insights Name>`                                       | Failure Anomalies pep-datahub-dev-scus-01-appi-sdar  |
| SQL Database                      | sqldb                | `pep-<service name>-<component/function>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-datahub-frontend-dev-scus-01-sqldb     |
| Load Balancer                     | lb                   | `pep-<service name>-<environment>-<region>-<internal/external>-<sequence number>-<resource acronym>` | pep-datahub-dev-scus-internal-01-lb        |
| Proximity Placement Group         | ppg                  | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus-01-ppg                 |
| Data Factory (V2)                 | adf                  | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus-01-adf                 |
| App Service                       | as                   | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-as            |
| App Service (Slot)               | N/A                  | `<App Service>/<Slot Name>`                                                           | pep-datahub-frontend/admin-dev-scus-01-as/staging    |
| Availability Test                 | avtest               | `<App Service>-<resource acronym>`                                                    | pep-datahub-frontend/admin-dev-scus-01-as-avtest     |
| Virtual Network                   | vnet                 | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus-01-vnet                |
| Route Table                       | rt                   | `pep-<service name>-<environment>-<region>-<scope>-<sequence number>-<resource acronym>` | pep-datahub-dev-scus-network-01-rt         |
| Virtual Machine Scale Set         | vmss                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-vmss          |
| Application Insights              | appi                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-appi          |
| Managed Identity                  | mi                   | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-mi            |
| SQL Server                        | sql                  | Same as ASG naming                                                                    | pep-datahub-frontend-dev-scus-01-sql                 |
| App Service Plan                  | asp                  | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-asp           |
| Recovery Services Vault           | asr                  | `pep-<backupmetallic><schedule|retention><region><number><type>`                     | pep-bronze-scus-01-backup                            |
| Public IP Address                 | pip                  | `<Assigned to Resource>-<resource acronym>`                                           | pep-datahub-dev-scus-external-01-lb-pip              |
| Log Analytics Workspace           | la                   | Same as ASG naming                                                                    | pep-global-operations-dev-scus-01-la                 |
| Action Group                      | ag                   | `pep-<service name>-<action>-<team>-<environment>-<region>-<sequence number>-<resource acronym>` | pep-datahub-sms-operations-dev-scus-01-ag  |
| Function App                      | func                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-func          |
| Activity Log Alert Rule           | alar                 | `<Log Analytics Name>-<alert scope>`                                                  | pep-sap/pfna/operations-dev-scus-01-la-vm-reboot     |
| Event Hubs Namespace              | evhns                | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-evhns         |
| SQL Virtual Machine               | sqlvm                | PepsiCo Compute Standards                                                             | pepvmname01                                           |
| Logic App                         | logic                | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-logic         |
| Event Grid System Topic           | egst                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-egst          |
| Image                             | it                   | `<vm name>-<service>-<version>-<resource acronym>`                                    | pepvmname01-sap-v1-it                                |
| Azure Workbook                    | aw                   | Same as ASG naming                                                                    | pep-governance-compliance-dashboard-prod-01-aw       |
| Azure Databricks Service          | dbms                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-dbks          |
| Container Registry                | acr                  | `pep<service name><component/function><environment><region><sequence number><resource acronym>` | pepdatahubsalesdevscus01acr               |
| Application Gateway               | agw                  | Same as ASG naming                                                                    | pep-shared-restricted-dev-scus-01-agw                |
| Template Spec                     | arm                  | Same as ASG naming                                                                    | pep-datahub-virtualmachines-dev-global-01-arm        |
| Dedicated SQL Pool                | syndp                | `pep<service><component/function><env><region><#><acronym>`                           | pepdatahubsalesdevscus01sundp                        |
| Synapse Workspace                 | synw                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-synw          |
| SSH Key                           | ssh                  | `<Resource Name>-<sequence>-<resource acronym>`                                       | pepvmname01-01-ssh                                    |
| Automation Account                | aa                   | Same as ASG naming                                                                    | pep-pfna/adlogging-dev-scus-01-aa                    |
| Event Grid Topic                  | evgt                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-evgt          |
| Shared Dashboard                  | sd                   | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-sd            |
| Language Understanding (LUIS)     | lu                   | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-lu            |
| App Gateway WAF Policy            | wafp                 | Same as ASG naming                                                                    | pep-datahub-detection-sso-dev-scus-01-wafp           |
| Kubernetes Service                | aks                  | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-aks           |
| Azure MySQL - Single              | mysqlsng             | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-mysqlsng      |
| Front Door WAF Policy             | fdfp                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-fdfp          |
| Front Door & CDN Profile          | afd                  | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-afd           |
| Service Bus Namespace             | sbns                 | Same as ASG naming                                                                    | pep-datahub-frontend/admin-dev-scus-01-sbns          |
| Connection                        | conn                 | `pep-<source>-<region>-<destination>-<sequence number>-<resource acronym>`            | pep-ergw-ae-frankfurt-01-conn                         |

> ### AWS Resource Naming Convention Catalog
| **Resource Type**                      | **Acronym** | **Naming Pattern**                                                                                   | **Example**                                               |
|---------------------------------------|-------------|-------------------------------------------------------------------------------------------------------|-----------------------------------------------------------|
| Activity Log Alert Rule               | alar        | `<Log Analytics Name>-<alert scope>`                                                                  | `pep-sap/pfna/operations-dev-scus-01-la-vm-reboot`       |
| Event Hubs Namespace                  | evhns       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-evhns`           |
| SQL Virtual Machine                   | sqlvm       | `PepsiCo Compute Standards`                                                                           | `pepvmname01`                                            |
| Logic App                             | logic       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-logic`           |
| Event Grid System Topic              | egst        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-egst`            |
| Image                                 | it          | `<VM Name>-<service>-<version>-<resource acronym>`                                                    | `pepvmname01-sap-v1-it`                                  |
| Azure Workbook                        | aw          | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-governance-compliance-dashboard-prod-01-aw`         |
| Azure Databricks Service              | dbms        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-dbks`            |
| Container Registry                    | acr         | `pep<service><component/function><env><region><seq#><resource acronym>`                              | `pepdatahubsalesdevscus01acr`                            |
| Application Gateway                   | agw         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-shared-restricted-dev-scus-01-agw`                  |
| Template Spec                         | arm         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-virtualmachines-dev-global-01-arm`          |
| Dedicated SQL Pool                    | syndp       | `pep<service><component/function><env><region><seq#><resource acronym>`                              | `pepdatahubsalesdevscus01sundp`                          |
| Synapse Workspace                     | synw        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-synw`            |
| SSH Key                               | ssh         | `<ResourceName>-<seq#>-<resource acronym>`                                                            | `pepvmname01-01-ssh`                                     |
| Automation Account                    | aa          | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-pfna/adlogging-dev-scus-01-aa`                      |
| Event Grid Topic                      | evgt        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-evgt`            |
| Shared Dashboard                      | sd          | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-sd`              |
| Language Understanding                | lu          | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-lu`              |
| Application Gateway WAF Policy       | wafp        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-detection-sso-dev-scus-01-wafp`             |
| Kubernetes Service (AKS)             | aks         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-aks`             |
| Azure DB for MySQL - Single Server    | mysqlsng    | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-mysqlsng`        |
| App Service (Slot)                    | N/A         | `<App Service>/<Slot Name>`                                                                           | `pep-datahub-frontend/admin-dev-scus-01-as/staging`      |
| Search Service                        | srch        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-srch`            |
| Availability Test                     | avtest      | `<App Service>-<resource acronym>`                                                                    | `pep-datahub-frontend/admin-dev-scus-01-as-avtest`       |
| Front Door WAF Policy                 | fdfp        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-fdfp`            |
| Front Door/CDN Profiles              | afd         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-afd`             |
| Service Bus Namespace                 | sbns        | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-sbns`            |
| Connection                            | conn        | `pep-<source>-<region>-<destination>-<seq#>-<resource acronym>`                                       | `pep-ergw-ae-frankfurt-01-conn`                          |
| NetApp Account                        | anf         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-anf`             |
| QnA Maker                             | qna         | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-qna`             |
| Cosmos DB Account                     | cosmo       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-cosmo`           |
| Web App Bot                           | asbot       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-asbot`           |
| Azure Cache for Redis                 | redis       | `pep-<service name>-<component/function>-<environment>-<region>-<seq#>-<resource acronym>`            | `pep-datahub-frontend/admin-dev-scus-01-redis`           |


### Sample Use Cases
  - TBD

### Additional Guardrails
  - Enforce lowercase only across all naming segments.
  - No underscores (_), spaces, or periods (.) are permitted.
  - For resources that cannot contain dashes (like storage accounts), use concatenated segments without separators.
  - Sequence numbers should be two digits, zero-padded (e.g., 01, 02).
  - Naming must comply with cloud vendor maximum length limits (63 chars).
  - Network-related resources must explicitly reference connected entities (e.g., VNET, subnet) in their names for clarity.
  - Avoid peering isolated or special network resources with routable enterprise VNets unless explicitly approved.
  - Naming must comply with cloud vendor maximum length limits (63 chars).
  - Network-related resources must explicitly reference connected entities (e.g., VNET, subnet) in their names for clarity.
  - Avoid peering isolated or special network resources with routable enterprise VNets unless explicitly approved.
  - Use the internal VM Name Generator tool for virtual machines and EC2 instance names to maintain standardization.
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 22 June 2025 (Sakshi Sharma)

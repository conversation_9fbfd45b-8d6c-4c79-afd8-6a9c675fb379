---
weight: 2
title: "Cloud Tagging Strategy"
date: 2025-01-07
tags: ["multi cloud", "tagging strategy", "", ""]
summary: "Cloud Tagging Strategy"
---

## Tagging Strategy Overview

**Name**: Tagging in Cloud
**Description**: Tagging in cloud is a critical practice for managing and organizing resources efficiently. Tags allow administrators and developers to categorize resources for easier management and cost allocation. Proper tagging is essential for security, automation, and operational efficiency.

### Lifecycle
  - Published Date: 19-05-2025

## Goals & Benefits
  - TBD
  
## General Pepsico Tagging Guidelines
  - The tag name has a limit of 512 characters and the tag value has a limit of 256 characters. For storage accounts, the tag name has a limit of 128 characters and the tag value has a limit of 256 characters.
  - Tag names can't contain these characters: <, >, %, &, \, ?, /
  - The tags should only have lowercase only.
  - No spaces should be used on the tag keys, replace spaces in the tag key/values with dashes “-”. E.g.: “data hub” will become “data-hub”.
  - New tags can be added for other purposes upon review and approval by Cloud Engineering and Cloud Operations.
  - Use single tag value, don’t apply a data structure with multiple values.

### Limitations
  - The following limitations apply to tags:
  - Not all resource types support tags. To determine if you can apply a tag to a resource type, see Tag support for Azure resources.
  - Each resource, resource group, and subscription can have a maximum of 50 tag name-value pairs.
  - Classic resources such as Cloud Services don't support tags.
  - Azure cost based on tags will not be retrofitted when adding or changing tag.
 
## Examples
  - TBD

### Sample Use Cases
  - TBD

### Additional Guardrails
  - TBD
  
## Ownership and Version Control

### Service Architect
  - <NAME_EMAIL>

### Version Control
  - v.1: 1 July 2025 (Sakshi Sharma)

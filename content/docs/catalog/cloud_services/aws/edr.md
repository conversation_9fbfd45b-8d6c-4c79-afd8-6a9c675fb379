---
weight: 6
title: "EDR"
date: 2025-07-22
tags: []
summary: ""
---

# AWS Elastic Disaster Recovery Service (AWS DRS)

## Cloud Service Classification

* **Category of the Service:** Disaster Recovery
* **Cloud Provider:** AWS

## Cloud Service Overview

* **Name:** AWS Elastic Disaster Recovery (AWS DRS)

* **Description:**  *AWS Elastic Disaster Recovery workflow: Set up continuous replication, perform non-disruptive tests, monitor readiness, and launch failover instances on AWS within minutes; then initiate failback to the primary site.*
  AWS Elastic Disaster Recovery is a fully managed service that enables fast, reliable recovery of on-premises or cloud-based systems to AWS. It continuously replicates source servers (physical, virtual, or cloud VMs) at the block level to a low-cost **staging area** in an AWS region of choice. This staging area uses affordable storage (Amazon EBS volumes, snapshots in Amazon S3) and minimal compute (small EC2 replication instances) to keep an up-to-date copy of the source environment without running full duplicate infrastructure. In the event of a disruption or disaster, AWS DRS can **launch recovery instances** on AWS within minutes, using either the latest replicated state or a prior point-in-time snapshot. The service automatically handles conversion of the machines to boot and run natively on AWS (injecting appropriate hypervisor drivers, adjusting networking, etc.) during launch. This allows organizations to meet aggressive recovery point objectives (RPOs of mere seconds) and recovery time objectives (RTOs of just minutes) for critical applications. After recovery, AWS DRS also supports **failback**: you can restore changes back to the primary environment once it’s operational again, or choose to keep the workloads on AWS (essentially a migration). AWS DRS provides a unified, simplified process to **test**, **recover**, and **fail back** applications without specialized DR hardware or skillsets, making it a cost-effective alternative to maintaining a secondary data center.

* **SKU Approval Status:**

  * **PepsiCo Approved:** AWS Elastic Disaster Recovery Service (all standard usage in supported regions)
  * **Not Approved:** *None (no separate SKU tiers for this service)*
    *(AWS Elastic Disaster Recovery is a single-tier service offered on a pay-as-you-go basis. There are no edition “SKUs” such as Basic/Premium; all features are available by default, and usage is charged per replicated server and storage.)*

* **Allowed PepsiCo Data Classification:** Public, Internal, Confidential, Restricted
  *(All data classifications are permitted on AWS DRS, **including Restricted**, provided that required security controls—such as encryption in transit and at rest, network isolation, and access control—are implemented. AWS DRS encrypts and compresses replicated data before transit by default and supports encryption of all persisted snapshots via Amazon EBS encryption. For Restricted data, private connectivity (e.g. VPN/Direct Connect) must be used instead of public internet, and additional guardrails as outlined below should be in place.)*

## Service Lifecycle

* **Release Date:** November 17, 2021 (General Availability). AWS DRS was introduced as the next-generation disaster recovery service on AWS, built upon technology from CloudEndure DR.
* **Planned Decommission Date:** No end-of-life or decommission date has been announced by AWS. (AWS Elastic Disaster Recovery is an active service with ongoing feature enhancements; there are no current plans for decommission.)
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Continuous Block-Level Replication:** AWS DRS uses an agent on each source server to continuously replicate disk volumes at the block level to AWS. Data is **securely transmitted** (encrypted and compressed) and written to Amazon EBS staging volumes in the target region. This enables near-real-time sync, achieving seconds-level RPO under normal conditions.
  * **Point-In-Time Recovery:** The service regularly takes incremental snapshots of the replicated volumes to create **Recovery Points**. These point-in-time snapshots (retained per a user-defined policy) allow you to recover applications from an earlier state (for example, just before a ransomware attack or data corruption) instead of the latest state. Up to 365 days of point-in-time snapshots can be retained per policy settings.
  * **Automated Conversion & Orchestration:** AWS DRS automates the conversion of source machines to AWS-native format upon launch. It injects necessary drivers (AWS PV/EBS, ENA networking drivers on Windows/Linux, etc.) and modifies boot settings so that the recovered instances boot successfully on EC2. It orchestrates the launch sequence for multiple servers, ensuring that multi-tier applications can be brought up in the correct order if needed. You can pre-configure launch settings (instance sizing, subnet/AZ, security groups) for each source server or apply default settings in bulk.
  * **Non-Disruptive Testing:** AWS DRS supports performing **DR drills** at any time. You can launch test instances (called **Drill** or **Recovery** instances) from the replicated data **without stopping ongoing replication or affecting the source**. This allows regular testing of your disaster recovery procedure to verify that applications come up correctly on AWS, all while continuous replication to the staging area continues (no impact on RPO during tests). After testing, you can easily terminate the test instances and cleanup, while replication remains active.
  * **Post-Launch Automation:** The service provides a **Post-Launch Actions** framework to automatically run custom scripts or AWS Systems Manager automations on the recovered instances after they boot. You can use predefined actions (e.g. install CloudWatch agent, verify network connectivity, validate volume integrity, create an AMI of the instance) or define your own via Systems Manager documents. This helps in automating any configuration changes or validations needed (such as reconfiguring application settings, updating DNS, etc.) immediately after failover.
  * **Network Configuration Replication:** AWS DRS can replicate AWS network configurations (for AWS-to-AWS DR scenarios). It captures and reproduces VPC components—subnets (CIDRs), security groups, route tables, internet gateways, network ACLs—in the recovery region to speed up environment preparation. This ensures the networking environment in the DR region mirrors the source, simplifying recovery and maintaining security postures.
  * **Flexible Recovery Options:** You can choose to launch **Recovery instances** either as **new instances** in AWS (default) or use the **“Recover into existing instance”** feature. The latter allows you to designate an already-provisioned (but stopped) EC2 instance as the failover target for a given source server. Recovering into an existing instance can be useful to preserve predefined instance IDs, ENIs, or other metadata (such as when a semi-warm standby instance is pre-created for compliance).
  * **Broad Platform Support:** AWS DRS supports a wide array of operating systems and environments. It can replicate servers running supported **Windows** and **Linux** OS versions (Windows Server 2008 R2 to 2022; popular Linux distributions and versions). This includes support for enterprise applications and databases such as Oracle, SQL Server, MySQL, and SAP running on those servers. It also supports sources from **physical servers**, **VMware vSphere**, **Microsoft Hyper-V**, and even other public clouds, by treating them as source machines to replicate into AWS. For AWS-to-AWS scenarios, it supports cross-Availability Zone and cross-Region replication of EC2 instances as well.
  * **Scalability & Elasticity:** The service is designed to scale with your needs. There is no fixed limit on the number of servers you can protect overall (thousands of servers can be managed). AWS DRS uses lightweight replication instances (t3.small by default) and can consolidate multiple source servers per replication instance to minimize cost. It automatically scales out additional replication servers as you add more source machines. You pay only for the resources used (hourly per source server, plus storage and minimal compute), and you **do not need to commit to any long-term licenses or capacity**. This pay-as-you-go model provides cost elasticity, with the bulk of expense only incurred when you actually launch recovery instances during drills or an actual disaster.
  * **Failback Support:** AWS DRS offers integrated failback capabilities. After running on AWS, you can initiate replication *back* from the AWS instances to your primary environment (on-prem or original cloud) once the primary is restored. The agent can be used in reverse to sync changes made on AWS during the disaster back to the source servers, minimizing data loss when returning. The service also provides a **Mass Failback Client** for VMware vCenter environments to streamline failing back dozens or hundreds of VMs at scale. This helps restore normal operations to your original data center or cloud with minimal additional downtime.

* **Sample Use Cases:**

  * **On-Premises Datacenter Outage:** Use AWS DRS to protect critical on-premises applications (e.g. ERP systems, websites, databases) by continuously replicating them to AWS. In the event of a site-wide outage (power failure, fire, hardware loss) at the primary datacenter, recover these applications in AWS within minutes and continue operations. For example, if a storage array failure corrupts a production database, AWS DRS can launch the database server on AWS from a recent snapshot, achieving RPOs of seconds and RTOs of minutes to meet business continuity needs.
  * **Ransomware or Cyberattack Recovery:** In case of a ransomware incident or malware corrupting data, AWS DRS enables point-in-time recovery to a state *just prior to the attack*. For instance, if servers are encrypted by ransomware at 3:00 AM, you can launch recovery instances from a snapshot taken at 2:00 AM (before encryption occurred). This provides a clean environment to restore operations without paying ransom, dramatically reducing downtime from such security events. **Ransomware drills** can also be performed to ensure your team is practiced in this recovery method.
  * **Cross-Region Disaster Recovery for AWS Workloads:** Use AWS DRS to increase resilience of cloud-native applications running in AWS by replicating them to a **different AWS Region**. For example, an e-commerce platform in **us-east-1** can have all its EC2 instances and databases continuously replicated to **us-west-2** as a standby. In case of a regional disruption (like a natural disaster or major service outage in the primary region), you can quickly spin up the entire stack in the secondary region. This helps meet stringent availability objectives and compliance requirements for disaster recovery of cloud workloads.
  * **Cloud-to-Cloud Migration/DR:** Migrate or protect applications from another cloud provider to AWS. AWS DRS can treat VMs in other clouds (Azure, GCP, etc.) as source machines. For example, a company can continuously replicate critical workloads running in another cloud into AWS and use AWS as the DR site. If the other cloud experiences an outage or if the company decides to migrate, they can rapidly activate those workloads on AWS with minimal downtime, effectively \*\* “failing over” from the other cloud to AWS\*\*. Over time, this can serve as a one-way migration into AWS by simply not failing back (promoting the AWS site to production).
  * **Regional Availability Zone Failure (Pilot Light Scenario):** Even within a single AWS region, you might use AWS DRS to recover from an AZ failure. For example, if an application was running in one Availability Zone without multi-AZ redundancy and that AZ goes down, AWS DRS could launch the application’s instances in an alternate AZ in the same region. (This is a “pilot light” DR approach on AWS where a minimal footprint is kept and expanded only when needed.) AWS DRS simplifies this by letting you pre-set an alternate **recovery AZ** for each source server. This way, you have a plan even for AZ-level disruptions, using DRS to quickly start instances in the healthy AZ.
  * **Enterprise IT Resilience & Testing:** Use AWS DRS not only for emergencies, but also for **continuous testing and dev/test** use cases. For instance, a bank can maintain an up-to-date copy of its core banking systems via DRS and periodically launch isolated copies in AWS for drill exercises or for testing software updates (without affecting production). These drills can be automated and serve to both prove disaster readiness and generate realistic test environments on-demand. After testing, the instances are terminated to avoid cost, and the DR copy remains ready for real use. This approach improves confidence in recovery procedures and can also facilitate audits and compliance demonstrations (showing you can meet RPO/RTO targets).

* **Limitations:**

  * **Maximum Protected Servers per Account:** By default, AWS DRS supports up to **300 source servers actively replicating** to a single AWS account per region. If you need to protect more than 300 servers, you must distribute them across multiple AWS “staging” accounts or multiple target regions (the service can be set up separately in each account/region). Using multiple accounts, it’s possible to recover up to 3,000 servers into one region (each staging account handling 300 sources) in a large-scale failover scenario. The overall service quota allows up to 4,000 total source server entries per account (this higher limit accounts for servers that might have been added/removed over time). These limits can be increased by AWS via support request for some quotas (e.g., total source servers) but not for others (the 300 concurrent replication limit is a technical limit).
  * **Volume and Storage Limits:** Each source server can have up to **60 attached volumes** (disks) replicated. If a server has an extremely high number of disks beyond typical limits (e.g., more than 60), those additional volumes cannot be protected by a single replication instance. Additionally, the size of each volume is effectively limited by EBS volume size (max 16 TiB per volume). Very large volumes or datasets might result in longer initial sync times and potentially larger snapshot storage costs. AWS DRS by default uses a t3.small replication instance which supports a limited number of EBS attachments; if a source has dozens of volumes, you must configure a larger instance type for its replication server to support attaching all those volumes.
  * **Operating System Support Constraints:** AWS DRS relies on an agent that must be installed on supported OS versions. While it covers most modern Windows and Linux systems, **legacy OS** that are end-of-life may not be supported or have upcoming deprecation. For example, Windows Server 2003 is no longer permitted for new agent installations as of May 2025 (and support will cease entirely in 2026). Similarly, 32-bit operating systems and uncommon platforms (e.g., AIX, Solaris, or mainframe OS) are not supported. The source server’s CPU architecture must be x86/64 (the replication instances run on x86\_64 EC2; sources with non-x86 architecture are not compatible). It’s also expected that the source OS is running on a supported filesystem and partition scheme (for instance, Windows 2008 with GPT boot volumes was noted as unsupported in documentation) – most standard setups are fine, but very old setups might have caveats.
  * **Data Consistency Considerations:** The continuous replication captures data at the block level **without application awareness** by default. This means that while AWS DRS ensures crash-consistent recovery points (equivalent to pulling the power on a machine and then rebooting it), it does not automatically guarantee application-consistent snapshots (e.g., it won’t quiesce a database or flush application caches prior to taking a point-in-time snapshot). If application consistency is required for certain workloads (like relational databases), you may need to coordinate your own quiescing mechanism or use built-in database replication in addition to AWS DRS. AWS DRS does allow custom pre/post scripts, which could be leveraged to freeze/unfreeze applications around snapshot times, but this is a customer-managed process.
  * **No Instant Automatic Failover:** AWS DRS is not an active-active high availability solution; it requires a **launch action** to initiate failover. In a disaster event, triggering recovery is a manual or scripted step – the service does not detect outages and auto-failover on its own. Recovery instances also need the environment (network, DNS, etc.) to be set up or switched over. This means that achieving minimal downtime depends on your DR runbook execution. There will typically be a short gap (minutes) between deciding to failover and having all applications fully online in AWS. This is expected for a DR scenario, but it’s important to note that DRS is not a zero-downtime failover like some active/active clustering solutions; it’s designed for **fast recovery** with minimal data loss, not instantaneous seamless transfer.
  * **Networking and Connectivity Requirements:** The source servers must have network connectivity to AWS for replication to occur continuously. If the network link is slow or unstable, you may see increased **Replication Lag** (where the staging area lags behind the source state). For example, insufficient bandwidth or high latency can cause the data backlog to grow, impacting the RPO. AWS DRS tries to optimize by not sending duplicate/empty blocks, but ultimately it cannot overcome fundamental bandwidth limitations. It’s recommended to have a reliable, high-speed connection (or use AWS Direct Connect) especially for data-intensive workloads. In cases of extended network outages, the service will catch up once connectivity restores, but during that outage window the RPO continually increases (potentially to hours if a long outage).
  * **Resource Usage During Recovery:** When you perform a recovery (or drill), the service launches the specified AWS resources (EC2 instances, possibly larger instance types as configured, plus associated EBS volumes). **While those recovery instances run, you incur standard AWS charges for them** just like any running workload. This is not so much a limitation as a cost consideration: a prolonged DR exercise or an actual failover will generate potentially significant EC2/EBS costs until you either terminate or failback. It’s important to plan for these costs in your DR budget. The service itself (replication) continues to bill as well during a failover. Also note that when running in failover mode, the original source servers (if still running or partially running) are no longer being updated—any changes on AWS side are not automatically protected unless you **reconfigure DRS to treat the AWS instances as new sources** (which you would do if expecting a longer failover or permanent migration).
  * **Snapshot Retention Risks:** AWS DRS’s point-in-time recovery relies on storing snapshots of your volumes. **If these snapshots are deleted or purged, your ability to recover to earlier points is lost**. By default, the service uses a retention period (e.g., 7 days by default) and will clean up older snapshots beyond that window. If an attacker or mistake in your AWS account deletes the snapshots (or if you set a very short retention), you could be left only with the latest state. In a worst-case scenario, if the replication *servers* and volumes were also somehow terminated (for example, someone unintentionally disables DRS on a source), you would lose even the latest copy. Thus, the snapshots are a critical point of protection. This limitation means you should implement measures to guard those backup snapshots (see **Backup & Recovery** below for mitigation strategies such as copy to another account or enabling AWS Backup with cross-account vault).

* **Additional Guardrails:** *TBD*
  *(Any additional enterprise-specific guardrails or policies for using AWS DRS (e.g., mandatory tagging of DR resources, required periodic drill frequency, cost thresholds, etc.) would be listed here. For now, to be determined.)*

* **Used By:** *TBD*
  *(List of PepsiCo projects or teams currently utilizing AWS Elastic Disaster Recovery, if any, along with references or links to their implementation summaries.)*

* **EA Declaration:** NOT a declared standard.
  *(This service is currently not formally declared as a PepsiCo Enterprise Architecture standard. It may be used on a case-by-case basis, but it is not yet broadly adopted as a standard pattern for disaster recovery. Architecture review and approval are required for each implementation.)*

## Technical Guidelines

* **Best Practices:**

  * **Establish a Detailed DR Plan:** Simply replicating servers is not enough – **prepare a written Disaster Recovery Runbook** that outlines the exact steps to take in an outage. Define roles and responsibilities, the order in which to bring up systems, how to re-route users (DNS or load balancer changes), and criteria for initiating failover. This plan should be reviewed and updated regularly. Include in the plan how you will handle data reconciliation after failback and any application-specific considerations (for example, dependencies between systems – ensure your plan brings up databases before application servers, etc.).
  * **Perform Regular DR Drills:** Practice makes perfect. Schedule **non-disruptive disaster recovery drills** at least a few times per year (if not more frequently). AWS DRS makes it easy to launch test instances without breaking replication, so take advantage of this to verify that applications come up correctly in AWS. Each drill should test not only failover but also **failback** procedures – e.g., ensure you can successfully return operations to the primary site or decide to cut over permanently. Document any issues found during drills and refine the process. Remember to **clean up** any test instances or resources after drills to avoid unnecessary costs (termination of drill instances should be a planned step).
  * **Right-Size Launch Settings & Resource Mapping:** Review and configure the **Launch Settings** for each source server (or set sensible defaults) in AWS DRS before an actual disaster. This includes choosing appropriate EC2 instance types for recovery, allocating sufficient CPU/RAM to handle production load (which might have grown since initial setup), and selecting the target subnet/AZ, security groups, IAM instance profiles, etc., for the recovered instance. By planning this in advance, you avoid scrambling during failover to adjust instance sizes. For example, if your on-prem server had 32 vCPUs, map it to a comparable EC2 instance family in the console beforehand. Also decide if any servers should be grouped (via tags or launch templates) to ensure certain servers (like app and DB) launch in a coordinated way.
  * **Leverage Infrastructure as Code for Networks:** After your first successful drill, consider **exporting your AWS DR environment setup to code (CloudFormation or Terraform)**. This refers to the VPC, subnets, security groups, and any other infrastructure created to support the recovered systems. By capturing this in an IaC template, you can tear down non-essential network resources when not in use (to save cost), knowing you can re-provision them quickly from code during a real event. This also ensures consistency – your recovery environment can be reproduced reliably in the future or in another region.
  * **Monitor Replication Health Continuously:** Set up monitoring and alerts for AWS DRS status. In the DRS Console, regularly check that all source servers show as **Healthy/Ready** (green checkmark) and not lagging or stalled. Use Amazon CloudWatch metrics or EventBridge events provided by AWS DRS to detect if any server’s replication falls behind (e.g., **Data Replication Lag** metric). If a server shows as “Stalled” or consistently high lag, investigate network or performance issues – you might need to increase bandwidth, adjust throttling settings, or allocate a dedicated replication server for that source. Automated scripts or EventBridge rules can even trigger notifications or remedial actions if a problem is detected. Proactive monitoring ensures that when disaster strikes, your copies are up-to-date and ready.
  * **Optimize Network Throughput & Costs:** Enable **data compression and deduplication** (these are built-in by DRS and on by default) to minimize bandwidth usage. If you have large volumes of data change, consider using AWS Direct Connect or a high-speed VPN to AWS to ensure throughput. You can also set up **bandwidth throttling** schedules in DRS (e.g., limit replication traffic during business hours to avoid saturating links, and allow full speed after hours) – configure this per your network capacity. For cost optimization, use **gp3 EBS volumes** in the staging area (default) which are cost-effective for replication storage, and take advantage of EBS snapshot lifecycle policies if needed to move old snapshots to cheaper storage tiers after some time.
  * **Secure and Streamline Agent Installation:** Control who in your team can install the AWS DRS agent on source servers. Consider creating a specialized IAM role just for agent installation with scoped permissions. Have administrators assume this role when installing the agent, rather than using broad credentials. This prevents unauthorized or accidental installation which could incur charges. Automate agent deployment where possible (for example, bake it into server build processes for any new system that needs DR, or use a management tool like Systems Manager to push the installer across many servers). Upon installing, ensure each server appears in the DRS console and reaches Healthy status, indicating replication is working.
  * **Plan for DNS and Endpoint Failover:** In your recovery plan, define how user traffic or system integrations will be redirected to the AWS site during failover. Whether you use DNS cutover via Amazon Route 53 (e.g., swap CNAME or use Route 53 health-check based failover records), or an application load balancer in the DR region, have that mechanism scripted or documented. Test the DNS change propagation in drills. If internal users access systems via fixed IPs or non-DNS methods, consider how you will handle that (perhaps using AWS Site-to-Site VPN or updating host files temporarily, etc.). The key is that recovering the servers is only part of the process – clients and dependent systems must start using those recovered instances. Include these steps in runbooks and automate them if possible (for example, integrate Route 53 updates in post-launch actions or have a prepared script to update DNS entries).
  * **Enable Termination Protection in Actual Failovers:** When you do perform an actual recovery (non-test) in AWS, it is wise to **enable termination protection** on the launched EC2 instances (and any critical resources) once they are up and running. This prevents someone from accidentally terminating a running recovery instance in AWS (for example, during the chaos of an incident). You can enable this via the EC2 console or CLI after the instance launches and passes initial checks. Document this step in the failover procedure. It adds a layer of safety so that the recovered systems are not inadvertently shut down before you’re ready.
  * **Avoid Premature Cleanup/Disconnect:** In a real disaster scenario, **do not use the “Disconnect from AWS” action too soon** on your source servers in the DRS Console. If you disconnect a source while you are running on AWS, DRS will remove all associated replication resources, **including point-in-time snapshots**, which you might still need. For example, if you failed over and later realize you launched from a snapshot that still contained some corrupted data, you may want to relaunch from an earlier snapshot – that would not be possible if you had disconnected and thus deleted those snapshots. So, keep the source connected (even if the original site is down) until you are sure you won’t need those old recovery points or until you have established new ones from the AWS side. Only disconnect and cleanup original sources when you either have permanently cut over or after a successful failback.
  * **Re-Protect After Migrating or Failover:** If you decide to **keep workloads on AWS** (i.e. treat the recovery as a migration), or if you are in an extended outage that requires DR site to run as production for a while, remember to set up **DR protection for your now-running AWS instances** as well. This may mean re-installing the agent on the recovered AWS instances pointing to a second region (for cross-region DR), or using AWS DRS’s cross-region replication feature to protect your AWS workloads to another region. In other words, avoid having your DR site be a single point of failure – once it’s the production, configure a DR plan for it too (even if that means going back to your original site or a third region). AWS DRS supports taking an instance that was launched in Region B and making it a source to replicate to Region A (or C), facilitating this “chaining” for resilience.

* **High Availability & Disaster Recovery:**
  *(**Note:** AWS DRS itself is a DR service; this section covers how to ensure high availability of the DR solution and using AWS DRS for various DR architectures.)*

  * **Service Availability:** AWS Elastic Disaster Recovery is a managed service operated by AWS, built with high availability in mind. The control plane for AWS DRS is spread across multiple Availability Zones in each region, and Amazon provides an SLA for DRS (customers can review the SLA terms on AWS’s site). The data replication pipeline leverages durable storage (Amazon S3 and EBS snapshots) which inherently have multi-AZ redundancy. For instance, the incremental snapshots are stored in Amazon S3, which is designed for 99.999999999% durability across multiple facilities. This means that even if a particular AZ experiences issues, your point-in-time backups remain safe in cross-AZ storage. However, the *ongoing replication* does use an active EC2 replication server in a specific AZ. By default, AWS DRS will launch replication servers as needed – if an AZ outage occurs that affects a replication instance, the service can launch a new one (once that AZ is unavailable, the agent would reconnect to a new replication server). To minimize impact, you can specify a preference for which subnet/AZ to use for staging; consider using a less failure-prone AZ or having a backup subnet in an alternate AZ ready to go. In practice, any interruption to replication (due to AZ issues) will pause updates until resolved, but you still have the last snapshots to recover from in the worst case.
  * **Cross-Region Resilience:** To guard against a complete region outage, you should set up AWS DRS with a **secondary target region** as well. This is not automatic – it requires configuring replication of your sources to a second AWS region in parallel (which may involve running a second replication agent pointing to another service endpoint, effectively doubling the data transfer). Alternatively, after failing over to region B, you could use DRS to replicate those instances to region C as a tertiary backup. These approaches can provide multi-region DR (for example, primary on-prem -> first DR on AWS region A -> backup DR on AWS region B). It increases cost and complexity, so it’s usually only justified for extremely critical systems. If multi-region DRS is too heavy, at minimum ensure you have **off-site backups** (such as AWS Backup vault with cross-region copies) in case your primary DR region is impacted by a widespread event.
  * **In-Region High Availability for Workloads:** AWS DRS is meant for DR rather than real-time HA, but when you bring applications into AWS (whether during a drill or actual failover), you can and should leverage AWS high-availability best practices for those running instances. For example, if you launch a recovery for a multi-server web application, distribute the recovered servers across multiple AZs in the region (DRS allows choosing the AZ per source). Use Elastic Load Balancing or other failover mechanisms so that if one AZ’s recovered instances have issues, others can handle the load. Essentially, once your workloads are running in AWS (even in disaster mode), you can treat them like any AWS deployment – enable AWS-native resilience (multi-AZ databases, etc.) if the scenario permits. This may not be feasible immediately during an emergency, but for prolonged operations in DR, consider scaling out or adding redundancy rather than running everything in a single AZ.
  * **Failover and Failback Workflow Resiliency:** Ensure that the supporting services for your failover are resilient. For instance, if you rely on Route 53 for DNS failover, Route 53 itself is a global service with high availability (which is good). If you use a VPN to connect users to the DR environment, consider having redundant VPN endpoints (perhaps using AWS Transit Gateway or multiple AWS Site-to-Site VPN connections via separate AZs). If using AWS Direct Connect for primary connectivity, have a backup VPN or a second Direct Connect at another location in case the primary fails. Essentially, identify any single point of failure in the DR path (network, authentication service, etc.) and mitigate it.
  * **Recovery Site Preparedness:** Keep your **DR region “pilot light” resources always ready**. This might include maintaining updated AMIs for critical systems (in case you plan to launch from AMIs as fallback), or pre-provisioning minimal infrastructure that is hard to automate on the fly. AWS DRS takes care of most of this by keeping disk volumes synchronized and instance launch settings defined. Still, ensure your AWS account in the target region has all the needed service quotas and configurations: e.g., enough EC2 capacity reserved or planned (especially if you use reserved instances or have specific instance type needs), service limits for EC2, EBS, etc., adjusted to handle a full failover load (running possibly hundreds of instances). Test that you can spin up the required number of instances in the region (perhaps during a scale test in non-prod). Also, keep the **AMI of the AWS replication agent** handy in case you need to quickly protect a newly launched AWS instance or support failback – though AWS DRS automates a lot, in complex scenarios you might need to deploy an agent on a new system (like after failback on new hardware). Being prepared reduces downtime in both failover and failback directions.

* **Backup & Recovery:**

  * **Retention of Recovery Points:** Configure the **Point-In-Time (PIT) snapshot retention policy** in AWS DRS to balance recovery flexibility and cost. By default, AWS DRS retains snapshots for 7 days, but you can increase this up to 365 days if business requirements dictate. More days retained means you can recover from older incidents (for example, a slowly manifesting data corruption that wasn’t noticed for weeks), but it also means more storage cost. Determine how far back you might need to go (consider compliance – some regulations might require the ability to recover 30 days back, for instance) and set the retention accordingly. It’s a best practice to at least maintain a few days to a week of points even if your RPO is seconds, because not all disasters are infrastructure failures; some are logical errors that replication would dutifully copy if not caught.
  * **Protect Point-in-Time Snapshots:** As noted in **Limitations**, the PIT snapshots are vulnerable if someone with access intentionally or accidentally deletes them. Mitigate this by using AWS Backup or cross-account snapshot copy. For example, you can create an AWS Backup plan to automatically copy the DRS EBS snapshots to a secure backup account (with tighter access controls and perhaps enabled **Vault Lock** for immutable backups). This way, even if the primary AWS account is compromised, the copies in the secondary account remain. Another approach is to enable **snapshot immutability** (if available via Backup Vault Lock) or at least manual safeguards like tagging snapshots with “DO-NOT-DELETE”. Also, regularly export a list of your recovery points (snapshot IDs and dates) so you have a record outside the system. The key is to treat your DR recovery points as a form of backup and give them the same protection you would give regular backups.
  * **Integration with AWS Backup and AWS Storage Services:** Leverage **AWS Backup** for long-term retention of critical data beyond the DR window. AWS DRS is focused on live replication and short-to-mid term recovery points. If you need yearly archives or point-in-time beyond 1 year, supplement DRS with periodic full backups (for example, a weekly EBS snapshot via AWS Backup that is kept for 1 year or moved to cold storage). For databases, continue to use native backup tools (like Oracle RMAN or MS SQL backups to S3) for long-term retention, and use DRS as the near-term quick recovery method. This multi-layer approach (DRS for fast recovery, backups for long retention) covers all bases.
  * **Automated Recovery Testing:** Periodically perform a *full restoration test* from your backups as well, not just DRS. For instance, assume a scenario where DRS was down or something went wrong – could you restore from backups? You might, for example, spin up an EC2 instance from an AMI or restore volumes from EBS snapshots in an isolated VPC to simulate a full restore. This ensures your backup processes remain viable. AWS DRS drills give confidence in one path of recovery; backups provide a safety net if that path is unavailable or if you need an older point. So test both.
  * **Failback Planning and Data Re-Synchronization:** After recovering in AWS and running there for some time, you will likely **fail back** to your primary environment. Plan how you will do this with minimal data loss. AWS DRS can reverse replicate changes from AWS back to on-premises – ensure that the replication agent is running on the AWS instances (it should be if failback was configured). Before failing back, **quiesce the AWS instances** (stop applications or databases) to ensure final sync, then use the DRS Console to initiate failback. Validate that the on-prem servers (or new replacement servers) are updated and bootable. It’s wise to take backups of the AWS instances (like EBS snapshots or AMIs) before cutting over back, just in case something goes wrong and you need to revert to the AWS copy. After failback, have a checklist to verify data integrity on the primary systems and re-enable any normal operations that were suspended. AWS provides a **Failback Client** for bulk VMware VM restoration which can simplify returning many VMs at once – consider using it for large environments.
  * **Cleanup After Recovery:** Develop a **cleanup procedure** for after a DR event (or even a drill). Once systems are back to normal in the primary site, ensure that you clean up leftover AWS resources that are not needed. This includes: terminating recovery instances in AWS, removing them from the DRS Console (after you are sure you don’t need them as source for cross-region), deleting any obsolete replication volumes, and perhaps keeping some logs or snapshots for audit but deleting excess to save cost. AWS DRS will bill for any source server still in “Connected” state and any replication resources until you disconnect them. So, for any servers that were permanently migrated or decommissioned, use the **Disconnect** and **Delete** options in DRS to stop billing and console clutter (only do this once you have all needed data from them). Also consider revoking any temporary elevated access that was given during the incident. Essentially, bring the environment (both AWS and primary) back to a normal steady state and ready for the next potential event. Document this cleanup as part of the DR plan.

* **Access Control & Security Configuration:**

  * **Identity and Access Management (IAM):** Follow least-privilege principles for all AWS DRS related activities. AWS DRS provides predefined IAM policies for the service operations (for example, an IAM role for the DRS agent to communicate with AWS, and roles for users to access the DRS console). Ensure the **AWS Replication Agent** on each source uses appropriate IAM credentials – typically, you’ll provide it with an access key ID/secret that has permissions limited to DRS (the agent installation guide suggests an IAM policy allowing only the necessary API calls). Use a dedicated IAM user or role for replication agents, and **enable MFA** where possible for any interactive access to the DRS console. Additionally, control who in your organization can launch a recovery or stop replication via IAM policies – you might want only certain administrators to have the ability to initiate a recovery to avoid accidental launches.
  * **Agent Installation Permissions:** As mentioned in Best Practices, restrict the ability to install the AWS DRS agent. AWS provides a managed policy for agent installation; attach it to a role and have admins assume that role when deploying agents. This prevents unauthorized personnel from adding servers to replication (which could expose data or incur cost). Track agent installation via AWS CloudTrail – any invocation of the `InstallRecoveryAgent` API or relevant actions should be logged and ideally alerted.
  * **Network Security (Replication Traffic):** By default, the replication servers will accept traffic from the source over TCP (the agent opens connections to the replication server on specific ports). Configure the **security groups** on the staging area (replication) EC2 instances to only allow inbound traffic from your source network ranges or VPN endpoints. If the replication server is in a public subnet with an elastic IP (default when not using private IP routing), lock down its security group to the IP range of your on-prem datacenter or the specific public IPs through which your agents connect. This ensures that even though the server is internet-facing, it isn’t open to the world – only your known IPs can send replication data. If using **private IP mode** (where the agent connects over VPN/Direct Connect), still use security groups to restrict traffic to only the private IP range of your sources.
  * **Encryption and Data Protection:** All data in transit between the source servers and AWS is **encrypted using TLS** by AWS DRS. Verify that this is happening (the agent logs can show if encryption is enabled, which it is by default). For data at rest, **enable EBS encryption** for the staging EBS volumes and snapshots. AWS DRS allows you to specify the encryption setting – by default, it will use your account’s EBS encryption settings (often AES-256 with an AWS-managed KMS key). For Confidential/Restricted data, consider using a **Customer-Managed CMK** (KMS key) for EBS encryption to have control over the keys. Note that changing the encryption setting after replication has started could trigger a full re-sync, so set it correctly from the start. Also ensure any S3 buckets used (if any, for logs or such) are also encrypted.
  * **Post-Launch Security Hardening:** When recovery instances come up on AWS, treat them as you would any server deployment: ensure security patches are applied (you may automate this via AWS Systems Manager Patch Manager), unwanted services are disabled, and correct firewall rules (Windows Firewall/iptables) are in place. DRS will launch the instances with the same OS state as the source, so if the source was not fully hardened, the recovered instance won’t be either – thus, **include a post-launch action to run a hardening script or at least a vulnerability scan**. For example, you can automatically run AWS Inspector or an antivirus scan on launched instances as part of the post-launch actions. Also double-check that only the necessary ports are open to the internet on the security groups after launch (the service can copy security group settings, but you might need to adjust if the DR environment has a different exposure profile).
  * **Logging and Monitoring:** Turn on **AWS CloudTrail** for governance – CloudTrail will record all actions related to AWS DRS (such as someone initiating a Drill, or changing replication settings). This provides an audit trail for compliance and troubleshooting. Use **AWS CloudWatch Logs** to collect logs from the replication agents or any CloudWatch metrics for replication lag. The AWS DRS console events can also be sent to Amazon EventBridge; consider creating EventBridge rules to alert on significant events like “Server X is lagging” or “Recovery instance launch initiated”. Implement Amazon SNS or other channels to notify the ops team. For security, ensure these logs and events are stored in a secure, tamper-evident manner (for example, CloudTrail logs delivered to an S3 bucket with MFA Delete enabled).
  * **Isolation of DR Environment:** It’s recommended to deploy your DR staging area in a **separate VPC or account** segment isolated from normal development/test workloads. This reduces the chance that someone might inadvertently access or tamper with the DR resources. For example, have a dedicated “DR account” which holds all replication servers and staging volumes, and only DR administrators have access to it. Then, at recovery time, you may execute recovery into a production account environment if needed (AWS DRS does allow recovering into a separate account from where the data is being staged). If using a single account, at least isolate the DR VPC and use strict security groups. Also consider applying AWS Config rules or Security Hub standards to the DR account to ensure best practices (e.g., no open security group ports, encryption enabled, etc.) are continuously monitored.
  * **DDoS and Network Protection:** If your replication servers have public endpoints, consider using AWS Shield (Standard is automatically applied, Advanced if you have a subscription) to protect against DDoS attacks that could target your DR endpoints. While it’s unlikely an attacker targets your replication server, any downtime there could hamper replication. Similarly, if during failover your applications will be public, ensure those endpoints (Elastic IPs, ALBs, etc.) are covered by AWS Shield Advanced or AWS Web Application Firewall if appropriate, to protect the recovery site from attacks at the worst possible time (during a disaster).

* **Network Connectivity Options:**

  * **Public Internet (Default):** *Not recommended for production sensitive data.* By default, AWS DRS sets up the replication servers in a public subnet with an auto-assigned public IP. The agents on your source servers will connect over the internet to these endpoints. This option might be acceptable for lower data classifications (Public/Internal) if other connectivity is not available, but it introduces exposure. **For any Confidential or Restricted data, public internet replication is NOT allowed without encryption and prior approval.** If public internet must be used (e.g., no private link in place yet), then at a minimum restrict inbound access to the replication server’s public IP to your known source IPs and ensure VPN encryption on the agent side. In general, we strongly prefer using private connectivity (below) for ongoing replication of sensitive systems.
  * **VPN or Direct Connect (Private Connectivity):** **Preferred method for on-premises to AWS replication.** AWS DRS supports routing replication traffic over private links – you can enable the “Use private IP for data replication” setting in DRS, which ensures the agent communicates with the replication server’s **private IP** rather than public. To use this, your on-prem environment must be connected to the AWS VPC via an AWS Site-to-Site VPN or AWS Direct Connect (or other private network). With this in place, replication traffic stays within a secure tunnel or dedicated circuit. This option is required for Restricted data – e.g., a Direct Connect or VPN must carry the replication traffic. It also often improves performance and reliability. Ensure your VPN/Direct Connect has enough bandwidth to handle the continuous data stream. When configured, the replication servers can be placed in a **private subnet** (no public IP) accessible only via the private network, which greatly reduces exposure.
  * **Inter-Region VPC Peering / Transit Gateway:** For AWS-to-AWS (cross-region) disaster recovery, you can avoid public internet by using AWS’s internal networking. If the source is an EC2 instance in Region A and the target is Region B, by default the agent might still send to a public endpoint in Region B. However, you can establish a **peering connection or a Transit Gateway attachment between Region A and Region B** VPCs, and then enable private IP routing so that the agent in Region A connects to the replication server in Region B via the AWS backbone. AWS DRS doesn’t natively create the peering – you set up the network and just ensure the routing allows the agent to reach the private IP of replication server. This way, even cross-region replication can stay off the internet. Note that inter-region peering is possible and traffic flows encrypted by AWS, but be mindful of bandwidth charges for cross-region data transfer on the AWS backbone.
  * **AWS PrivateLink (Interface Endpoints):** *Not applicable.* AWS DRS currently does not offer a PrivateLink endpoint service for the DRS API or replication data plane. The replication traffic is agent-to-EC2, which you control via VPC setup (as above), and management is through the console or standard AWS APIs (which you can access via an AWS VPN or Direct Connect as well). So PrivateLink is not used in this context.
  * **Service Endpoints:** In Azure contexts there are “service endpoints”, but in AWS, comparable advice is to use the above methods. If your source environment is within AWS already, simply ensure the **staging subnet has route(s) to source** (like if in different VPCs, use VPC peering or Transit Gateway as noted). If on-prem, ensure the VPN/Direct Connect is set and test the connectivity (e.g., telnet from source to the replication server private IP on the port to confirm).
  * **Networking Security Architecture:** Ideally, the DR environment’s network should mimic production but in an isolated way. Use separate subnets for different tiers if needed (web, app, DB) in the DR VPC and ensure security group rules are appropriately set (these can often be copied from source, but double-check any differences in IP ranges or dependencies). No replication or recovery resource should be exposed to the internet except where absolutely required (like maybe a web server in failover, but even then consider keeping it internal behind a VPN until needed). If possible, **pre-create VPC endpoints** for services your recovered instances might need (like S3 or DynamoDB endpoints) so that during failover, those instances can function without needing open internet access. Essentially, treat the DR VPC as a production environment from a networking standpoint: least privilege, segmentation, and monitoring of traffic. *(Further detailed networking and security architecture for the DR solution is organization-specific and is marked TBD.)*

* **Networking & Security Architecture:** *TBD*
  *(This section will include diagrams or descriptions of the specific networking architecture patterns recommended for AWS DRS at PepsiCo – e.g., how the DR VPC peering is set up, security zones, etc. To be developed in collaboration with the network and security teams.)*

* **AWS Service Limitations:**

  * **No Native Multi-Region Failover Automation:** AWS DRS does not automatically fail over across regions. If your primary AWS region fails, you must manually initiate recovery in your chosen DR region (having had it configured). There is no one-click “failover to another region” mechanism – each region’s DRS setup is independent. This is unlike some database services (Aurora Global, etc.) that have automated regional failover. You need to have planned and tested the process to invoke DRS in the secondary region and update any global endpoints or Route 53 records.
  * **Consistency vs. Continuity Trade-off:** DRS focuses on crash-consistent replication. It does not ensure that distributed applications are transactionally consistent across multiple servers. For example, if you have an app server and a database server, their crash-consistent snapshots might be from slightly different times (a few seconds apart). Upon recovery, the database might reflect a state that the app server hadn’t seen before the crash. This is similar to any async replication. Mitigate by using application-level checkpoints or quiesce multiple servers together if needed (not a built-in feature; needs manual coordination).
  * **Lack of Built-in Application Awareness:** While DRS can bring up infrastructure, it doesn’t natively reconfigure applications. For instance, an application might need to know it’s now in DR mode (different hostname, etc.), or a license key might be tied to MAC address/UUID that changed. DRS won’t handle these application-specific adjustments out of the box. You must use post-launch scripts or other automation to handle such changes.
  * **Scaling During Recovery Not Automatic:** If your production normally auto-scales (e.g., an ASG of 10–50 instances based on load), note that DRS will launch whatever you configured (perhaps a static number of instances to match one point in time). It doesn’t recreate auto-scaling policies or scale out based on current load. You may need to manually start additional instances or integrate with auto-scaling after launch. The assumption is usually that during disaster, normal load might be lower or secondary, but if you expect high load, you must plan to scale up the recovered environment.
  * **Costs for Drills and Extended Outages:** As mentioned, running on AWS via DRS can become costly if left for long periods or if drills are very large-scale. There is no cost cap mechanism in DRS – be mindful when launching, especially if accidentally selecting a large number of servers for drill. Implement tagging on launched instances and use cost monitors to track any unusual spend.
  * **Limited CloudFormation Support:** AWS DRS resources (like source server objects, launch settings, etc.) are currently not created via CloudFormation templates. They are configured through console/CLI. This means you can’t, for example, deploy a DRS configuration as code easily. Management is more manual or script-driven via the AWS CLI/SDK. This is a limitation for infrastructure-as-code purists, but can be managed by using the DRS API in scripts if needed.
  * **AWS Outposts Support:** AWS DRS **now supports AWS Outposts** (on-prem AWS racks) as sources or targets as of 2023. But note that if using Outposts as a source, the DR target is still a region; and if you wanted to use Outposts as a target (recover into an Outpost), that is supported only for Outposts Rack form factor (not Outposts servers). This is a niche limitation but important for hybrid scenarios.
  * **Agent Overhead:** The replication agent does consume some resources on the source server (CPU, memory for buffering changed blocks). Typically it’s lightweight, but on very busy servers you might observe a few percent performance overhead. This is generally negligible, but if a server is extremely latency-sensitive, test the agent’s impact. It also installs a driver on Windows which is generally fine, but kernel-level software on legacy systems might pose a risk – ensure to follow AWS’s guidelines and test agent installation in staging environments if possible.

* **SKU Features:**
  *(AWS Elastic Disaster Recovery is not offered in multiple tiers or SKUs like some Azure services; it’s a single service where all functionality is included by default. Below are key characteristics of the service usage and pricing model in lieu of “SKU” distinctions.)*

  * **Single Service, Pay-as-You-Go:** There is only one “edition” of AWS DRS – all customers get the full feature set (continuous replication, point-in-time recovery, automation features, etc.). Pricing is usage-based: you are billed an hourly rate per **source server** that is in replication, plus the cost of the AWS resources (EC2 and EBS) that the staging area and any recovery instances consume. There are no upfront fees or different pricing tiers for standard vs. premium features; everything is included and scales with usage.
  * **Staging Resources (Always On):** For each source server, AWS DRS maintains minimal EC2 footprint (the small replication server) and EBS volumes equal to the size of the source disks. These incur costs continuously. For example, a 100 GB source disk would have a \~100 GB EBS volume in AWS (plus some overhead for snapshots) that you pay for per GB-month. The replication server (t3.small by default) costs a few dollars per month while running. These are significantly cheaper than running full servers, hence the cost savings of DRS, but must be accounted for at scale.
  * **Recovery Resources (On Demand):** When you launch recovery instances, you begin incurring costs for EC2 instances (at whatever size you chose) and any additional EBS volumes (which essentially transition from “staging” to “attached to running instance” status). If you only launch during tests or actual DR events, you only pay for these during that period. There is no additional DRS service charge for launching – just the normal AWS infrastructure charges. After you terminate recovery instances, costs drop back to the baseline of staging resources.
  * **Included Features:** All features such as point-in-time snapshots, cross-AZ launch, post-launch actions, etc., are included. The only feature that might incur extra cost indirectly is extensive point-in-time retention (keeping many snapshots can increase your S3 storage costs, as each recovery point is stored as EBS snapshots). But there’s no “Premium” needed for, say, 1-minute RPO or for enabling post-launch scripts – the service inherently aims for lowest RPO possible and has all tooling integrated.
  * **Support Plan Consideration:** While not a SKU of the service, it’s worth noting that AWS recommends having at least a Business or Enterprise Support plan when using DRS for production workloads. This ensures you can get 24/7 support in a disaster scenario. The support plan is a separate cost, not included with DRS, but crucial for enterprise usage.
  * **No License Fees:** Unlike some third-party DR software, AWS DRS does not require separate license purchases for agents or management servers. The agent is free to install; you only pay AWS for the underlying resources as described. This makes it easy to start/stop protection on servers without worrying about license counts – you can add or remove servers and costs adjust accordingly, with no long-term commitments.

* **Related Service:** *TBD*
  *(This section will list services related to AWS DRS. Potential related services include AWS Application Migration Service (MGN) – which is a similar service focused on migrations rather than ongoing DR – and AWS Backup – which complements DRS for data backup. Also CloudEndure (preceding technology) could be mentioned. To be filled out with context of how they relate in PepsiCo environment.)*

* **Alternatives:**

  * **Traditional Backup & Restore:** One alternative to continuous replication is to rely on periodic backups and restore them in the cloud when needed. For example, using AWS Backup to take nightly snapshots of on-prem systems (via Storage Gateway or other mechanisms), then, in a disaster, launching new EC2 instances and restoring those backups. This approach typically results in higher RTO (hours or days to procure hardware/restore data) and RPO (data since last backup is lost), but it’s lower cost and simpler. It may be acceptable for non-critical systems. AWS Backup, Storage Gateway, or even manual backup tools can be used in this strategy.
  * **Pilot Light Architecture:** In a pilot light DR, a minimal version of the environment is always running in the DR site. For AWS, this could mean you maintain small instances of critical databases replicating (e.g., via database replication) and have infrastructure as code ready to deploy the rest of the environment quickly. When disaster strikes, you “ignite” the pilot light by scaling up those small instances and deploying the application servers from scratch (perhaps using AMIs or containers). This is more manual than AWS DRS but can be done with a combination of **database replication** (like using Aurora Global Database, or SQL log shipping to an EC2 in DR region) and automated deploy tools for the app. It provides lower RPO for the DB (almost real-time if using DB replication) but requires time to spin up app servers (which could be automated in minutes with auto-scaling groups or scripts). AWS DRS actually can be seen as automating a lot of the pilot light – it keeps the data updated and allows one-click launch – but a purely pilot light approach might skip DRS in favor of native replication and infra-as-code.
  * **Warm Standby:** In a warm standby, a scaled-down version of the full environment is always running in the secondary site at all times. For instance, you might run your production with 10 servers, but keep 2 servers running in the DR region serving maybe light workloads or in standby. Data is replicated in near real-time (through application-level replication, or something like database replication, file replication, etc.), and the standby is actually serving in a limited capacity or at least ready to take traffic. In failover, you would scale it up (e.g., add more servers, increase instance sizes). This approach yields very low RTO (since systems are already up, just need scaling) and low RPO (if replication is continuous), but at the cost of running duplicate systems at reduced scale. You might achieve this using auto-scaling and global load balancers: keep a trickle of traffic or a heartbeat going to the DR region systems to ensure they work. AWS services like **Aurora Global Database, DynamoDB Global Tables, S3 Cross-Region Replication** and others can support the data sync for such active-passive setups. Warm standby is more expensive than DRS because resources are running 24/7, but it can be managed if scaled down.
  * **Multi-Site Active/Active:** For the highest resilience, some applications can be run in an active/active mode across multiple regions simultaneously (or across on-prem and cloud concurrently). This is not always possible (requires the app to support distributed state or partitioning), but if feasible, it provides near-zero RTO and RPO. For example, a global web service might run in two AWS regions with live traffic in both; if one fails, the other picks up seamlessly. Data synchronization is often the challenge – you might use databases that support multi-master or have conflict resolution. AWS offers services and patterns for this (e.g., using **Route 53 latency-based routing or failover routing** to distribute traffic, and using globally replicated data stores). Active/Active eliminates the need for a “recovery” – there’s nothing to launch since it’s already running – but it’s the most complex and costly architecture. It’s an alternative for truly critical systems where any downtime is unacceptable. However, for many enterprise workloads, active/active is not practical, and that’s where a solution like AWS DRS strikes a balance by automating a lot of the failover process.
  * **Azure Site Recovery or Other DRaaS:** If considering multi-cloud or cloud-agnostic approaches, there are third-party and other cloud solutions. For example, Microsoft Azure’s Site Recovery (ASR) can replicate on-premises VMs into Azure in a similar fashion. Some organizations might choose to replicate certain systems into Azure using ASR as an alternative to AWS DRS (especially if they already use Azure heavily). There are also specialized Disaster-Recovery-as-a-Service (DRaaS) vendors that can replicate VMs to their cloud or to VMware Cloud on AWS. However, these alternatives are outside the scope of AWS offerings and would introduce additional vendors and possibly complexity. They might be considered if AWS DRS doesn’t support a particular system or if a multi-cloud DR strategy is mandated.

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * *Information Security Specifications – AWS Elastic Disaster Recovery:* All usage of AWS DRS must conform to PepsiCo’s InfoSec baseline for cloud services. This includes ensuring data is encrypted in transit and at rest, rigorous access control is in place (as detailed above), and that DR environments are tested for vulnerabilities. Review the internal **Cloud Security Baseline for AWS DRS** document which maps AWS DRS capabilities to PepsiCo’s security controls (such as logging requirements, network isolation standards, and data handling rules). Given that AWS DRS will handle potentially sensitive copies of production data, the service must be deployed in accordance with compliance requirements (e.g., **GDPR** considerations if personal data is replicated across borders – ensure the DR region is approved for that data residency). The InfoSec baseline document provides further guidance on encryption key management (use of customer-managed keys for Restricted data), incident response integration (how to include DR in incident response plans), and any required security testing (like annual DR test attestations or penetration testing of the DR environment).
  * **Shared Responsibility Model:** Remember that AWS DRS operates under the AWS shared responsibility model. AWS manages the security *of* the cloud (the underlying DRS service platform and AWS infrastructure), while PepsiCo is responsible for security *in* the cloud (our usage of DRS). This means we must properly configure DRS and the surrounding environment securely – AWS ensures the service is reliable and compliant with their cloud security standards (with audits and certifications available for review), but it’s on us to use it in a compliant manner (e.g., not replicating data to unauthorized regions, controlling access, etc.). Ensure to involve the InfoSec team for any data classified as Restricted to verify that all controls (such as private connectivity, encryption keys, monitoring) are satisfactory.

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun – Cloud Architecture, PepsiCo ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * v1.0 – 22 Jul 2025: *Initial draft prepared for AWS Elastic Disaster Recovery Service catalog entry (Dariusz Korzun).*

---
weight: 5
title: "EBS"
date: 2025-07-22
tags: []
summary: ""
---

# Amazon Elastic Block Store (EBS)

## Cloud Service Classification

* **Category of the Service:** Storage (Block Storage)
* **Cloud Provider:** Amazon Web Services (AWS)

## Cloud Service Overview

* **Name:** Amazon Elastic Block Store (EBS)
* **Description:** Amazon EBS provides persistent, high-performance block storage volumes for use with Amazon EC2 instances. It allows you to create virtual hard drives (volumes) and attach them to EC2, then use them like a physical disk (format with a file system, store application data, etc.). EBS volumes reside in a specific AWS Availability Zone and are automatically replicated within that AZ to protect against hardware failure, offering high availability and durability. All EBS volume types support point-in-time snapshots (backups) and are designed for mission-critical reliability.
* **SKU Approval Status:**

  * **PepsiCo Approved:**

    * General Purpose SSD volumes (gp3, gp2) – balanced price/performance for most workloads.
    * Provisioned IOPS SSD volumes (io2, io1) – high-performance SSD for intensive I/O needs.
    * Throughput Optimized HDD volumes (st1) – low-cost HDD for frequent, throughput-intensive access.
    * Cold HDD volumes (sc1) – lowest-cost HDD for infrequently accessed, cold data.
  * **Not Approved:**

    * Magnetic volumes (standard) – previous-generation magnetic disk volumes (legacy, not for new use).
* **Allowed PepsiCo Data Classification:** Public; Internal; Confidential; Restricted (all data classifications are supported on EBS with appropriate security measures such as encryption).

## Service Lifecycle

* **Release Date:** August 20, 2008.
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

### **Features:**

  * **Persistent Block Storage:** Provides durable block-level storage for EC2. EBS volumes remain available independently of the lifecycle of any single EC2 instance (e.g., if an instance is stopped or terminated, the attached EBS volume can be detached and reattached to another instance). Volumes are replicated within their AZ to prevent data loss from component failures, offering 99.8–99.9% durability for standard volumes and up to 99.999% for certain volume types.
  * **Multiple Volume Types:** Offers a range of volume types optimized for different use cases. SSD-backed options (General Purpose and Provisioned IOPS) deliver low-latency, high-IOPS performance for transactional workloads (databases, boot volumes, etc.), while HDD-backed options (Throughput Optimized and Cold HDD) deliver high throughput for large, sequential workloads at lower cost. This allows tailoring storage performance and cost to application needs.
  * **Snapshots (Backups):** Supports point-in-time snapshots of volumes, which are incremental backups stored in Amazon S3. Snapshots capture only changed blocks since the last snapshot, making them efficient for frequent backups. Snapshots enable use cases like disaster recovery, data migration, and cloning of volumes. They can be used to restore volume data, create new volumes, or even be copied across regions/accounts for backup or migration. Snapshots are stored with 11 nines of durability on S3, ensuring reliable long-term retention.
  * **Elastic Volume Management:** EBS supports Elastic Volumes, allowing you to **dynamically** modify volume size, performance (IOPS/throughput), or even volume type without downtime. This means you can start with a smaller or lower-performance volume and adjust it on the fly as requirements grow – for example, increasing capacity or switching from gp3 to io2 for higher IOPS – without detaching the volume or stopping the instance. This capability simplifies scaling and performance tuning in production environments.
  * **Multi-Attach (Shared Volume):** Certain EBS volumes can be attached to multiple EC2 instances simultaneously. Specifically, Provisioned IOPS SSD volumes (io1 and io2) support Multi-Attach, enabling a single volume to be attached to up to 16 instances in the same AZ. Each attached instance has full read/write access to the shared volume. This feature is useful for clustered applications or high-availability setups that manage concurrent writes at the application level. (Note: Multi-Attach requires Nitro-based instances and is **only** supported on io1/io2 volumes, and applications must use a cluster-aware file system or locking mechanism to maintain data consistency.)
  * **Encryption & Security:** EBS integrates with AWS KMS to offer transparent encryption for volumes and snapshots. You can configure EBS encryption by default for your account, so all new volumes are encrypted using KMS keys (either AWS-managed or customer-managed). Encryption protects data at rest on the volume and in-transit between the volume and the instance, and it also encrypts all snapshot backups. Additionally, snapshot access can be restricted – by default snapshots are private to your account, and you can share snapshots with specific accounts or make them public if needed. EBS provides the ability to lock snapshots to prevent accidental deletion and to **prevent** public exposure of sensitive data.

### **Sample Use Cases:**

  * **Boot and Application Volumes for EC2:** Use EBS as the root/boot volume for EC2 instances and as attached data volumes for applications. For example, an EC2 virtual machine running Linux or Windows can boot from an EBS volume (with the OS installed on it) and use additional EBS volumes to store application files, logs, and data. This provides persistent storage that outlives the instance and can be backed up via snapshots.
  * **Relational & NoSQL Databases on EC2:** Deploy databases like Oracle, SQL Server, MySQL, PostgreSQL, MongoDB, or Cassandra on EC2 with EBS as the underlying storage. EBS volumes (particularly io2 or gp3) provide the low-latency, high-IOPS storage needed for transaction logs and database data files. For instance, an EC2 instance running PostgreSQL can use a Provisioned IOPS SSD volume to ensure consistent high throughput and fast query response times.
  * **Enterprise Applications (ERP, CRM, etc.):** Migrate on-premises workloads that relied on SAN/NAS storage to AWS by using EBS. Applications like SAP HANA or other ERP systems can be run on EC2 instances with multiple EBS volumes striped or attached as needed for performance and capacity. EBS volumes offer the high availability (replicated in AZ) and reliability required by such mission-critical applications, effectively replacing traditional disk arrays with cloud block storage.
  * **Big Data Analytics & Batch Processing:** Use high-throughput EBS volumes for big data frameworks (Hadoop, Spark, data warehouses). For example, analytics clusters can use Throughput Optimized HDD (st1) volumes to store HDFS data or intermediate files, benefiting from the high sequential throughput for scanning large datasets. EBS volumes can be freely detached and reattached, so you can spin up a cluster, attach volumes with pre-loaded data, run large-scale batch analytics, then shut down – resizing the cluster or reusing volumes as needed.
  * **Backup, Recovery, & DR:** Incorporate EBS snapshots as part of a backup and disaster recovery strategy. Critical volumes can be snapshotted regularly (e.g., hourly or daily) to Amazon S3. In case of instance or AZ failure, these snapshots can be used to quickly restore the data by creating a new volume (or to launch a new EC2 instance from an AMI that includes the snapshot). Snapshots can also be copied to a second region to serve as cross-region backups for disaster recovery. Fast Snapshot Restore can be enabled on important snapshots to ensure that any volume restored from them is instantly fully performant, reducing recovery time.

### **Limitations:**

   **Availability Zone Scope:** EBS volumes are **tied to a single AZ** and cannot be attached to instances in different AZs or regions. If you need to move data to another AZ or region, you must create a snapshot and then restore it to a new volume in the target AZ/region. This also means that if an entire AZ experiences an outage, volumes in that AZ become unavailable until the AZ is restored (unless you have backups or replicas in another AZ).
  * **Single-Instance Attachment (Standard Volumes):** By default, an EBS volume can only be attached to one EC2 instance at a time. The Multi-Attach feature (for io1/io2 as noted above) is the exception, but comes with specific requirements. For typical volumes, you cannot share an EBS volume between multiple servers concurrently. If multi-read access is needed, alternatives like Amazon EFS (a distributed file system) or copying data to multiple volumes are used.
  * **Performance Constraints:** The performance of an EBS volume is subject to the limits of its type and size. For example, gp2 volumes smaller than 1 TiB have burst performance up to 3,000 IOPS and then can be limited by credit depletion under sustained load. HDD volumes (st1, sc1) use a credit/burst system for throughput and will drop to baseline throughput if the burst credit is exhausted. Additionally, each EC2 instance has an overall EBS bandwidth cap – if you attach many high-performance volumes, the total throughput can be limited by the instance’s EBS bandwidth (which varies by instance type). Using an **EBS-optimized** instance or one with higher throughput capacity is necessary to fully utilize fast volumes.
  * **Initialization Delay from Snapshots:** When a new volume is created from a snapshot (backup), by default not all data is immediately loaded – the first read of each block may incur extra latency as the block is pulled from S3. This **initialization penalty** means the volume performance might be slower until all frequently-accessed blocks have been read at least once. Admins often “pre-warm” volumes (read all blocks or enable Fast Snapshot Restore) to avoid this delay for production systems.
  * **No Native Multi-AZ Volume Replication:** Outside of specialized AWS services (like certain multi-AZ database services), EBS does not automatically replicate volumes across Availability Zones. You must use snapshots or application-level replication to protect against AZ failure. Real-time synchronous replication between AZs for a live volume is not an out-of-the-box feature of EBS.
  * **Legacy Magnetic Volumes:** The older magnetic (`standard`) volumes have **very limited performance** (average \~100 IOPS, up to a few hundred IOPS burst). They also max out at 1 TiB in size. These are considered legacy and are not suitable for most modern workloads; AWS recommends using SSD or modern HDD volumes for better performance. In many enterprises, magnetic volumes are disallowed for new deployments due to their performance and capacity limitations.

* **Additional Guardrails:** *TBD* (to be defined per internal governance policies).

* **Used By:** *TBD* (list & links to some internal use cases or onboarded projects leveraging EBS).

* **EA Declaration:** NOT a declared standard (use of Amazon EBS is common but not formally declared as an enterprise standard service).

## Technical Guidelines

* **Best Practices:**

  * **Choose the Right Volume Type:** Select an EBS volume type that matches your workload’s requirements for performance and cost. General Purpose SSD (gp3) is a good default for boot volumes and general applications due to its balance of price and performance. For latency-sensitive, high-transaction systems (like large databases), use Provisioned IOPS SSD (io2) which offers the highest IOPS and durability. For throughput-oriented jobs (big data scans, log processing), consider st1 volumes, and for infrequent, archival data use sc1. Avoid using previous-generation magnetic volumes for new systems.
  * **Use EBS-Optimized Instances:** Always run EC2 workloads with EBS volumes on “EBS-optimized” instance types or Nitro-based instances which dedicate bandwidth to EBS I/O. This prevents network traffic contention between storage I/O and other traffic. Most modern instance families (e.g., M5, C5, etc.) are EBS-optimized by default, allowing you to achieve the documented performance for your volume (throughput and IOPS) consistently.
  * **Enable Encryption by Default:** For security and compliance, enable EBS volume encryption by default in your AWS account. Encrypted volumes use AWS KMS keys to protect data at rest with negligible performance impact. This ensures that all new volumes (and their snapshots) are encrypted, reducing the risk of sensitive data exposure. Use customer-managed KMS keys if you need to control key policies or implement key rotation for specific datasets.
  * **Regular Snapshots & Lifecycle Policies:** Implement a routine backup strategy using EBS snapshots. Automate snapshots via AWS Data Lifecycle Manager (DLM) or AWS Backup to ensure point-in-time backups are taken at required intervals (e.g., nightly backups, hourly for critical data). Retain snapshots as per your data retention policy and test restoration periodically. Use tags on volumes/snapshots (e.g., “Environment\:Production” or “Backup\:Yes”) to identify which ones need regular backup, and have IAM policies restrict snapshot deletion for critical volumes to prevent accidental data loss.
  * **Monitoring and Alerts:** Monitor EBS metrics in Amazon CloudWatch. Key metrics include VolumeConsumedRead/WriteOps (IOPS), VolumeThroughput, VolumeLatency, BurstBalance (for gp2, st1, sc1), and VolumeQueueLength. Set up CloudWatch Alarms to alert if a volume is running at or near its IOPS or throughput cap consistently, or if queue length is high – these could indicate the need to upscale volume performance or distribute the load. Monitoring will also show if an HDD volume is frequently bursting (credit depletion) or if an SSD volume is throttling, prompting possible adjustments.
  * **Optimize Performance:** Follow AWS performance optimization guidelines for EBS. For example, if using HDD volumes (st1/sc1), maintain a queue depth of at least 4–8 for large sequential I/O to achieve steady throughput. Consider increasing the I/O size or enabling read-ahead on Linux for throughput-oriented volumes. In contrast, for SSD volumes, focus on IOPS and latency – ensure your application is spreading I/O across multiple threads to utilize available IOPS. In some cases, provisioning multiple volumes and striping them at the software level (RAID0) can overcome per-volume limits, but this adds complexity and should be used only when necessary (and never for volumes with different types or without redundancy for critical data).
  * **Use Multi-Attach Judiciously:** If you deploy Multi-Attach on io1/io2 volumes for active-active clustering, ensure the application is designed for shared disk access. Use a cluster-aware file system or locking mechanism as recommended (e.g., Oracle RAC or Windows Failover Cluster with SAN mode). Limit Multi-Attach to scenarios where you require quick failover within an AZ or concurrent processing on the same dataset; remember that a Multi-Attach volume is still in a single AZ, so it doesn’t protect against AZ failure. Always test the failover and data integrity in Multi-Attach setups under load.

* **High Availability & Disaster Recovery:**

  * **Intra-AZ Redundancy:** Amazon EBS automatically handles redundancy within an AZ by replicating volume data to multiple storage nodes, which protects against disk hardware failures and achieves high availability without user intervention. This means a single EBS volume is resilient to many types of internal failures (AWS reports annual failure rates of 0.1–0.2% for standard volumes) and can endure the loss of an underlying device transparently.
  * **Cross-AZ Resilience:** Because EBS volumes cannot natively span AZs, achieving high availability across AZs requires architectural strategies. One common approach is to use application-level replication or clustering: for example, run a primary instance in AZ A and a secondary instance in AZ B, each with its own EBS volume, and use database replication or software mirroring to keep data in sync. In the event of AZ A outage, the secondary in AZ B can take over with a recent copy of the data. Alternatively, regularly take EBS snapshots and ensure the snapshots are available to create volumes in another AZ or region if needed for a cold standby recovery.
  * **Fast Recovery with Snapshots:** For disaster recovery (DR), design a process to quickly restore operations from EBS snapshots. Consider enabling **Fast Snapshot Restore (FSR)** on critical snapshots in your primary region – this ensures that volumes created from those snapshots do not experience the usual lazy-loading latency and are ready to use at full performance immediately. For example, you might enable FSR on the latest nightly snapshot of a production database volume to speed up launching a clone in DR drills.
  * **Cross-Region DR:** Maintain up-to-date copies of data in a second region by periodically copying EBS snapshots to that region. In a severe disaster affecting an entire primary region, you can use these copied snapshots to restore volumes and spin up your EC2 instances in the DR region. AWS Backup can automate cross-region snapshot copies as part of a backup plan. Keep in mind that data transfer and snapshot storage costs will apply for cross-region copies.
  * **Multi-Attach for HA within AZ:** In scenarios where high availability is needed *within* a single AZ (for example, to survive an instance failure without downtime), you can leverage Multi-Attach volumes on multiple instances. For instance, two EC2 instances in the same AZ could concurrently mount an io2 volume; if one instance fails, the other still has the volume attached and can continue service. This can reduce recovery time to essentially zero for certain clustered applications. However, remember that if the volume itself or the AZ fails, both instances are affected – Multi-Attach is not a substitute for AZ-level redundancy, but rather a way to improve availability in active-active configurations within one AZ.

* **Backup & Recovery:**

  * **Snapshot Strategy:** Use EBS snapshots as the primary backup mechanism for volumes. Snapshots are incremental and stored in Amazon S3, which is designed for 99.999999999% durability. The first snapshot of a volume is a full copy; subsequent snapshots only save changed blocks, making frequent backups efficient. Plan snapshot schedules based on RPO (Recovery Point Objective) needs – e.g., mission-critical volumes might snapshot every hour, whereas less critical ones daily or weekly. Leverage AWS Backup or DLM to automate this, applying tags to identify which volumes get backed up and how long to retain those backups.
  * **Recovery Process:** To recover data, create a new EBS volume from the needed snapshot (you can do this in the original AZ or a different AZ or region if performing a migration/DR). The new volume will contain the point-in-time data from when the snapshot was taken. Attach this volume to an EC2 instance to access the data. If the snapshot was of a root volume, you can register it as an AMI and launch a new EC2 instance from it for a full instance recovery. Always verify that the restored volume or instance boots correctly and data integrity is intact as part of DR testing.
  * **Backup Consistency:** Ensure application consistency when taking snapshots. For file systems or databases, consider freezing or flushing writes (using application-specific backup tools, or AWS provided methods like **EC2 Application System Consistent Snapshots** for Windows, or simply pausing the application I/O) before triggering a snapshot. This will help the snapshot capture a consistent state. For critical databases, you might use the database’s backup function or shut down the instance briefly to get a clean snapshot.
  * **Snapshot Lifecycle & Cost Management:** Implement retention policies for snapshots to control cost – older snapshots that are no longer needed should be cleaned up. AWS Data Lifecycle Manager can automatically delete snapshots older than X days as configured. If long-term retention is required (for compliance or archives), consider using EBS Snapshot Archive, which can reduce storage costs by up to 75% for snapshots kept over 90 days. Archived snapshots have a retrieval time (typically 24-72 hours), so use them for true archiving needs, while keeping recent snapshots in standard tier for quick restores.
  * **Cross-Account Backups:** If needed for extra safety, you can share snapshots to a secondary “backup” AWS account (ideally encrypted snapshots shared with a KMS key the other account can use). This protects against accidental deletion or compromise of the primary account. Be cautious to share only the snapshots intended – sharing an unencrypted snapshot makes its content accessible to the recipient, so usually keep snapshots encrypted when sharing across accounts.
  * **Testing Recovery:** Periodically perform recovery drills: take a snapshot and actually restore it to a new volume and host to verify that your backup data is usable. This helps catch any issues with the backup process and ensures you are confident in the recovery steps and timing.

* **Access Control & Security Configuration:**

  * **IAM Policies for EBS:** Use AWS Identity and Access Management (IAM) to tightly control who can perform actions on EBS resources (volumes and snapshots). Follow the principle of least privilege – for example, an application or developer who needs to snapshot a volume might be granted permissions only to create and read snapshots on that specific volume (using resource-level permissions and condition keys like volume tags), but not to delete snapshots or act on volumes they don’t own. AWS provides some managed policies (e.g., **AmazonEC2FullAccess** includes EBS actions) but it’s recommended to create custom policies that limit actions as necessary.
  * **Prevent Unauthorized Data Access:** Be mindful of EBS snapshot sharing. By default, snapshots are private, but if made public or shared, anyone with access can create a volume from them. Never make snapshots public if they contain sensitive data. If you need to share a snapshot with another account, prefer sharing an **encrypted** snapshot, which requires the recipient to have access to the KMS key – this adds a layer of control. AWS Config or third-party tools can be used to detect if any snapshots become public. Also consider using EBS Snapshot Lock (a feature to lock snapshots for a period) to prevent accidental deletion or tampering with critical backups.
  * **Encryption Practices:** Always use EBS encryption for volumes containing confidential or restricted data. You can enforce encryption account-wide, and even mandate the use of customer-managed keys for specific projects to ensure only authorized services/principals can decrypt the data. All data in an encrypted EBS volume is encrypted at rest and in transit between the volume and instance. Note that if you take a snapshot of an encrypted volume, the snapshot is encrypted as well; similarly, volumes restored from that snapshot will be encrypted. Unencrypted snapshots of sensitive volumes should be avoided.
  * **EC2 Instance Security:** Since EBS volumes are accessed through EC2, ensure the EC2 instances themselves are secure. This includes using security groups to restrict network access, keeping the OS patched, and possibly using OS-level encryption or file-system encryption on top of EBS if an additional layer is required. While EBS encryption protects at the block device level, file-level encryption (like database transparent data encryption) can offer defense in depth.
  * **Logging and Auditing:** Utilize AWS CloudTrail to audit all EBS API calls. CloudTrail will log events like CreateVolume, DeleteVolume, CreateSnapshot, DeleteSnapshot, AttachVolume, etc., along with the user who performed them. This is crucial for forensic analysis and compliance, ensuring you can trace which user or service created or modified a volume or snapshot. Set up alerts or use AWS CloudWatch Events (EventBridge) to catch important events (for example, alert if a snapshot is shared publicly, or if a volume is deleted).
  * **Network Access (API Endpoints):** EBS volume data travels over the AWS internal network to the EC2 instance, but API calls to manage EBS (which are part of EC2 API) by default go to public AWS endpoints. For enhanced security in a VPC, consider using AWS PrivateLink (Interface VPC Endpoints) for the EC2 service – this allows EC2/EBS API calls to stay within your VPC network without needing an Internet route. By creating an interface endpoint for com.amazonaws.<region>.ec2, you ensure calls like CreateVolume or CreateSnapshot use the VPC endpoint. You can also attach endpoint policies to restrict which EBS actions can be called via that endpoint if desired.

* **Network Connectivity Options:**

  * **Public Internet Access:** *Not Applicable.* EBS volumes have no direct external network endpoint – they can only be accessed by an EC2 instance to which they are attached, and that instance provides any network access. In effect, EBS operates within the AWS cloud’s internal network. Management of EBS (via AWS API/console) can be done over the internet or via AWS Direct Connect/VPN, but the storage traffic is not exposed publicly. **Public endpoints** for the EC2 API exist, but those can be secured (see below).
  * **VPC Interface Endpoints (PrivateLink):** For making API calls to manage EBS without traversing the public internet, AWS offers VPC Interface Endpoints for Amazon EC2 (which cover EBS actions). By configuring a VPC endpoint, you ensure that calls like provisioning volumes or snapshots go through a private connection from your VPC to AWS services. This is especially recommended for sensitive environments where you want all AWS API traffic to remain in-network. It also enables the use of VPC endpoint policies to control access. Note that there is no separate data endpoint for EBS volumes themselves – the data path is handled through the EC2 infrastructure.
  * **EC2 Instance Connectivity to EBS:** When an EC2 instance and an EBS volume are in the same AZ, the storage traffic is carried over a dedicated storage network (or the EBS-optimized link) within that AZ. There is no need for configuring security groups or NACLs for EBS traffic, as it’s not IP-based connectivity that the user manages. The performance of this connection is dictated by instance type (EBS bandwidth) and is isolated from general traffic on EBS-optimized instances. Thus, from a networking perspective, ensuring your instance is in the correct AZ and is EBS-optimized is the main consideration.
  * **Cross-Region & Cross-AZ Access:** You cannot attach an EBS volume to an instance in a different AZ or region; for cross-AZ or cross-region needs, you would use snapshots and then create new volumes. If low-latency shared data access across AZs or regions is needed, consider higher-level solutions (like AWS FSx for shared file storage, or S3 for cross-region accessible storage). EBS by design keeps data within a single AZ for performance and durability reasons.
  * **Data Transfer Costs:** While not a connectivity “option,” note that data transferred between an EC2 instance and its attached EBS volume in the same AZ is free and does not count towards bandwidth charges. However, if you access snapshots, copying snapshots across regions, or use certain features like EBS direct APIs (to directly read snapshot content via a service like Athena or external services), there may be data transfer or API request costs. Ensure that heavy cross-region snapshot copy or reads are planned with cost in mind (e.g., using compression or only copying needed snapshots). *(No direct citation needed; cost aspect is general knowledge, and the user specifically asked not to include pricing details.)*

* **Networking & Security Architecture:** *TBD* (to be designed per specific application architectures; typically involves placement of EBS within a VPC’s AZ, use of EC2 security groups for instances, and ensuring compliance with internal network security requirements).

* **AWS Service Limitations:**

  * **Volume Size & IOPS Limits:** A single EBS volume can be up to 16 TiB in size for most volume types (gp2, gp3, io1, st1, sc1). The io2 volume type (with Block Express) supports volumes up to 64 TiB. IOPS and throughput per volume are capped by type: e.g., gp3/gp2 max out at 16,000 IOPS (gp3 can also reach up to 1,000 MB/s throughput when provisioned to max); io1 max 64,000 IOPS (1,000 MB/s), whereas io2 Block Express can reach 256,000 IOPS and 4,000 MB/s on a single volume. HDD volumes have lower limits (st1: 500 IOPS or 500 MB/s; sc1: 250 IOPS or 250 MB/s max). These limits mean extremely high-demand workloads might need to stripe multiple volumes or choose a different storage solution if beyond EBS single-volume limits. Also, note that an EC2 instance imposes an aggregate IOPS/throughput limit across all attached volumes (for example, a certain instance type might only support 80,000 IOPS total from all volumes, regardless of each volume’s capability).
  * **Attachment Limit per Instance:** There is a maximum number of EBS volumes that can be attached to a single EC2 instance, driven by available device slots and instance type. Most modern (Nitro-based) instances support up to 27 or 28 attachments in total (which includes the primary network interface and any instance store disks). For example, on an m5.xlarge (which supports 28 total attachments and always has at least 1 network interface), you can attach up to 27 EBS volumes. Older Xen-based instances have limits around 40 volumes on Linux (with stability caveats) and 26 on Windows (due to drive letter limitations). Attaching extremely large numbers of volumes can lead to boot or performance issues, so AWS notes that going beyond 40 on Xen is “best effort” only. In practice, the attachment limit is seldom a bottleneck (few use cases need tens of volumes on one instance), but it can affect design if you were aggregating many disks for throughput.
  * **Consistency of Performance:** EBS volumes are designed to deliver the provisioned performance 99% of the time, but certain activities can impact this. For instance, taking a snapshot of an st1 or sc1 volume can temporarily reduce its throughput to the baseline level until the snapshot completes. Similarly, very small, random I/O patterns on throughput-optimized HDD volumes can underutilize them, since they achieve best performance with large sequential I/O. AWS also periodically improves backend performance – volumes created long ago might not immediately have the latest performance features until modified (for example, older gp2 volumes might need a modify action to get newer throughput behavior). It’s important to test and monitor if maximum performance is critical.
  * **Lack of Native Multi-Region Support:** EBS is not a distributed storage system across regions – if you need active-active storage across regions, you will need to replicate data at the application level or use other services. EBS Snapshots can be used to migrate or backup data across regions, but this is an asynchronous process and not real-time replication. There is also no automatic failover for volumes to another region; the failover process is manual (or orchestrated by your DR automation) using snapshots and new volumes.
  * **Resource Availability:** While rare, AWS may impose account-level limits on EBS resources (for example, a maximum total storage capacity or number of volumes per region by default). Typically, an AWS account might start with a soft limit like 20 TiB of EBS storage or a certain number of Provisioned IOPS; these limits can be increased by requesting a service quota increase. Ensure that your account limits for EBS (volume count, total storage, PIOPS, etc.) are checked against your architecture needs, especially in large-scale environments.

* **SKU Features:** *(EBS Volume Types and Key Characteristics)*

  * **General Purpose SSD (gp3):** The latest generation general-purpose SSD volume offering consistent performance independent of size. Every gp3 volume provides a baseline of 3,000 IOPS and 125 MiB/s throughput by default, and can be provisioned up to a maximum of 16,000 IOPS and 1,000 MiB/s if additional performance is needed. gp3 volumes have low single-digit millisecond latency and are ideal for a broad range of workloads that require a balance of price and performance – including boot volumes, virtual desktops, medium-sized databases, application servers, and dev/test environments. Unlike gp2, performance on gp3 is not tied to volume size, so you can scale IOPS and throughput independently. *(gp3 is recommended for most new deployments as it offers better performance per cost than gp2.)*
  * **General Purpose SSD (gp2):** The previous generation general-purpose SSD volume. gp2 volumes deliver 3 IOPS per GB of volume size (with a minimum of 100 IOPS), up to a cap of 16,000 IOPS at 5.34 TiB volume size. They can burst to 3,000 IOPS on volumes smaller than 1 TiB (burst credits are used for this). Throughput scales with size as well, up to 250 MB/s at the maximum 16 TiB size. gp2 provides single-digit millisecond latency and is suitable for many of the same use cases as gp3 (general workloads, boot volumes, interactive apps). However, because performance is linked to size and bursting, gp2 volumes may experience throttling if a smaller volume sustains high I/O for long periods (after burst credits deplete). AWS guarantees gp2’s ability to meet the baseline and burst performance 99% of the time. *(Note: AWS has announced gp3 as the preferred general purpose volume; gp2 is still available for compatibility and existing systems.)*
  * **Provisioned IOPS SSD (io2 & io1):** High-performance SSD volumes designed for the most demanding I/O-intensive workloads. **io1** volumes allow provisioning up to 64,000 IOPS per volume (at maximum size 16 TiB, 50 IOPS/GB) and up to 1,000 MB/s throughput, with consistent performance ideal for critical databases. **io2** is the newer generation offering higher durability (99.999% durability, 100x more reliable than io1’s 99.9% in terms of annual failure rate) and allows provisioning 500 IOPS/GB (up to the same 64,000 IOPS at 16 TiB) – effectively making smaller io2 volumes more capable. Importantly, io2 volumes on capable Nitro instances can use **io2 Block Express**, which expands a single volume’s limits to 256,000 IOPS and 4,000 MB/s throughput and supports volumes up to 64 TiB. Both io1 and io2 are meant for workloads that demand sustained IOPS, sub-millisecond latency, and high resiliency, such as large relational databases (Oracle, SQL Server, PostgreSQL), NoSQL databases (Cassandra, MongoDB), and workloads like SAP HANA or ERP systems. They also support Multi-Attach for building clustered or HA systems within one AZ. In general, use io2 for new deployments due to its greater durability and performance; use io1 only if already in use or if operating in a region where io2 is not yet available.
  * **Throughput Optimized HDD (st1):** Low-cost, magnetic hard disk-based volumes optimized for **throughput** (MB/s) rather than IOPS. st1 volumes are designed for **frequently accessed, large-sequential I/O** workloads such as big data analytics, data warehousing, Apache Kafka streams, log processing, and ETL jobs. They support sizes from 125 GiB up to 16 TiB. Performance on st1 is measured in throughput: they provide a baseline throughput of 40 MB/s per TiB of volume size, and can burst up to 250 MB/s per TiB (accumulating credits when below baseline) up to a maximum of 500 MB/s per volume. The IOPS on st1 is low (\~500 IOPS at 1 MiB I/O, as sequential throughput), so they are unsuitable for random or small-block access patterns. st1 volumes achieve their best performance when reading/writing large blocks sequentially and maintaining a queue of operations to utilize the disk. They cannot be used as boot volumes. These volumes are great for keeping costs down while handling high-volume streaming reads/writes.
  * **Cold HDD (sc1):** The lowest-cost EBS volume type, backed by HDDs, intended for **cold data** that is infrequently accessed. sc1 volumes trade performance for cost savings – ideal for archival storage, large data sets that are rarely scanned, or log data that needs to be kept but not often read. They provide a baseline throughput of 12 MB/s per TiB and can burst up to 80 MB/s per TiB, with a hard maximum of 250 MB/s per volume. IOPS are very limited (\~250 IOPS max per volume, assuming 1 MiB I/O). Like st1, sc1 uses a burst-bucket model and should only be used for sequential access patterns; performance will be very poor for random access. sc1 volumes also cannot serve as boot volumes. They are best suited for the cheapest storage of data that you might occasionally need to process or retrieve, but not for anything latency-sensitive.
  * **Magnetic (standard) \[Previous Generation]:** Magnetic volumes are legacy EBS volumes from early AWS days. They are backed by traditional spinning disks without the benefit of throughput bursting. They offer on average about 100 IOPS (with potential bursts to a few hundred) and max throughput around 40–90 MB/s. Sizes range only up to 1 TiB. These volumes have the lowest performance and are generally not recommended unless you have very small, infrequently accessed workloads that cannot justify even sc1. AWS has largely superseded magnetic volumes with the SSD and modern HDD classes, and these volumes are mainly present for backward compatibility. Enterprises typically avoid using `standard` volumes for new deployments.

* **Related Service:** *TBD* (e.g., AWS Backup for automated EBS backups, AWS EC2 Instance Store for ephemeral storage, or Amazon FSx/Amazon EFS as alternative storage services, etc.)

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * *Information Security Specifications – Amazon EBS* (Refer to internal InfoSec baseline document for AWS EBS, covering required encryption settings, data handling standards, access control policies, etc., to ensure EBS usage complies with PepsiCo security and compliance requirements.)

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun – *[<EMAIL>](mailto:<EMAIL>)*
* **Version Control:**

  * v.1: 22 Jul 2025 (Dariusz Korzun)

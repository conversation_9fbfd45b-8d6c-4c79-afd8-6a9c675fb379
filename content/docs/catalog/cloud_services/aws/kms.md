---
weight: 8
title: "KMS"
date: 2025-07-22
tags: []
summary: ""
---

# AWS Key Management Service (AWS KMS)

## Cloud Service Classification

* **Category of the Service:** Security
* **Cloud Provider:** AWS

## Cloud Service Overview

* **Name:** AWS Key Management Service (AWS KMS)

* **Description:**
  AWS Key Management Service (KMS) is a fully managed service that enables you to easily create and control the cryptographic keys used to encrypt your data. It provides centralized key management with hardware security modules (HSMs) protecting keys, helping you secure data across AWS services and in your applications.

* **SKU Approval Status:**

  * **PepsiCo Approved:**

    * AWS Key Management Service (AWS KMS)

  * **Not Approved:**

    * AWS CloudHSM
    * AWS Payment Cryptography

* **Allowed PepsiCo Data Classification:**
  Public; Internal; Confidential; Restricted

## Service Lifecycle

* **Release Date:** November 12, 2014
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Centralized Key Management** – Manage cryptographic keys (encryption keys and key pairs) in a single service, including creation, rotation, disabling, and fine‑grained access control via key policies and IAM permissions. AWS KMS supports both symmetric keys for encryption/decryption and asymmetric keys for digital signing or encryption, with all key material stored securely in HSMs.
  * **Integration with AWS Services** – Tightly integrated with numerous AWS services (Amazon S3, EBS, RDS, Redshift, etc.), allowing those services to encrypt data at rest using KMS‑managed keys. AWS services use envelope encryption: data is encrypted with a data key that is itself encrypted under a KMS master key, so applications and AWS services can offload key management to KMS while protecting large data efficiently.
  * **Secure HSM‑Backed Storage** – Protects keys using FIPS 140‑3 Level 3 validated hardware security modules. Your key material never leaves the secure boundary of AWS KMS HSMs in plaintext form. KMS is designed such that even AWS personnel cannot retrieve your plaintext keys, and all cryptographic operations are performed within the HSMs for high security assurance.
  * **Logging and Auditing** – Every use of a KMS key is recorded in AWS CloudTrail, providing an audit trail of who accessed which key and when. This integration allows tracking of key usage, detection of unauthorized access attempts, and supports compliance reporting by capturing detailed logs of encryption and decryption operations.
  * **Automatic Key Rotation** – Supports optional automatic yearly rotation of key material for customer‑managed keys (symmetric keys generated by KMS) to meet compliance requirements. AWS‑managed keys (created by AWS services in your account) are rotated annually by AWS automatically. Rotating keys helps limit potential data exposure by periodically refreshing keys, though rotation for asymmetric and HMAC keys or imported key material must be managed manually.

* **Sample Use Cases:**

  * **Encrypting Data at Rest in AWS** – Use KMS customer‑managed keys to encrypt data stored in AWS services like S3 buckets, EBS volumes, RDS databases, and Redshift warehouses. For sensitive or regulated data (e.g., Confidential or Restricted), you can maintain control by using your own KMS keys instead of AWS‑managed keys, ensuring that only your organization’s principals can decrypt the data.
  * **Application‑Level Encryption (Envelope Encryption)** – Build applications that protect sensitive data by using KMS to generate data encryption keys. The application uses the plaintext data key to encrypt sensitive data (files, fields, backups) and stores only the encrypted data key. When needed, the app calls KMS to decrypt the data key (after verifying the caller’s permissions) and then uses it to decrypt the data. This approach offloads heavy key management tasks to KMS while keeping encryption/decryption of bulk data local for performance.
  * **Digital Signing and Verification** – Generate asymmetric key pairs in KMS for digital signature use cases, such as code signing, document signing, or token signing. The private key never leaves KMS, but your applications can use KMS APIs to produce digital signatures on data. This ensures high‑value signing keys (like a certificate authority’s private key or JWT signing key) are kept in a secure HSM, and only authorized services can request signing or verification using those keys.
  * **Bring Your Own Key (BYOK)** – Import cryptographic key material from your on‑premises HSM or external key management system into AWS KMS to use with cloud workloads. This can be required for compliance when keys must be generated or stored in a specific environment. KMS allows importing keys and using them as KMS keys so that cloud services can encrypt/decrypt with them while you maintain an external root of trust. For example, a financial organization might generate a master key in an on‑prem HSM and import it into KMS, enabling AWS services to use it for encryption while the original key material is retained offline.

* **Limitations:**

  * **Data Size Limit for Encryption** – AWS KMS can directly encrypt only relatively small plaintext payloads (up to 4 KB) with a KMS key. This means large data objects cannot be sent directly to the KMS Encrypt API. Instead, clients should use envelope encryption (generate a data key and use that to encrypt large data) to handle big files or high‑volume data efficiently. Relying on envelope encryption reduces network calls to KMS and avoids performance bottlenecks for large payloads.
  * **Throttling and API Limits** – KMS enforces request quotas on cryptographic operations and key management API calls per account/Region. By default, an account is limited to a certain number of encrypt/decrypt requests per second and a maximum number of keys, though these limits can be increased by AWS upon request. Exceeding the API request rate will result in throttling (HTTP 429 errors), so applications should implement exponential backoff and use batching or caching of data keys to stay within service quotas.
  * **Key Material Export Restriction** – Customer‑managed KMS keys cannot be exported from the service — the key material is locked inside AWS KMS and only usable via KMS APIs. There is no mechanism to retrieve or back up the plaintext of a KMS key (aside from using the specialized CloudHSM‑based custom key store feature). This design enhances security but means that if a key is scheduled for deletion and the waiting period expires, the key material is irretrievably lost. Administrators must be careful when deleting keys, and should use key aliases or key rotation rather than immediate deletion if data encrypted under those keys might be needed in the future.
  * **Service Scope** – AWS KMS is a regional service. Keys are created in a specific AWS region and are not automatically replicated to other regions. If you need a key available in multiple regions for resiliency or latency, you must explicitly use the multi‑Region key feature or create separate keys in each required region. Additionally, KMS is not designed for certain specialized cryptography domains such as payment card processing (e.g., PIN block management); AWS provides separate services (AWS CloudHSM or AWS Payment Cryptography) for those use cases, which are not utilized under the standard KMS service in our environment.
  * **Not a General Secrets Store** – AWS KMS itself does not store arbitrary user secrets or certificates (it only stores cryptographic keys). For managing application secrets (passwords, API keys, etc.), use AWS Secrets Manager or Parameter Store, which encrypt secret values using KMS keys. Similarly, manage X.509 certificates using AWS Certificate Manager. KMS’s role is limited to key management and cryptographic operations, rather than acting as a general vault for configuration secrets or certificates.

* **Additional Guardrails:** TBD

* **Used By:** TBD (list & links to some onboardings)

* **EA Declaration:** AWS KMS is a declared standard service for cryptographic key management in the enterprise.

## Technical Guidelines

* **Best Practices:**

  * **Use Customer‑Managed Keys for Sensitive Data** – Prefer creating customer‑managed KMS keys for any sensitive or Restricted data. This allows full control over key policies, rotation, and lifecycle. Use separate KMS keys for different applications or data domains (e.g., separate keys per environment or per application) to limit the scope of each key. This segmentation helps enforce least privilege — services and users only get access to the specific keys they require.
  * **Least Privilege Access Control** – Define and enforce strict key policies and IAM policies for KMS keys. Separate roles for key administration vs. key usage: for example, a security team role might manage key setup (create, rotate, delete keys) while application roles only have encrypt/decrypt permissions on those keys. Avoid using wildcards (\*) in KMS policies; instead specify exact principals and actions allowed. Consider requiring multi‑factor authentication (MFA) in key policies for highly sensitive operations (KMS supports adding an MFA condition to policies).
  * **Enable CloudTrail and Monitoring** – Ensure AWS CloudTrail is enabled in all regions to log KMS API calls, and actively monitor these logs. Configure alerts (via CloudWatch Alarms or AWS Security Hub/GuardDuty) for unusual KMS usage, such as a spike in decryption requests or any use of keys outside expected hours. Regularly review key usage logs and AWS Config rules to detect if keys are unintentionally exposed or if anyone attempts to disable or delete keys without proper change control.
  * **Key Rotation and Lifecycle Management** – Enable automatic key rotation (annual rotation) for customer‑managed symmetric keys that protect long‑term sensitive data, in line with compliance requirements. For keys that do not support automatic rotation (asymmetric keys, HMAC keys, or imported keys), establish a manual rotation procedure (e.g., generate a new key annually and update applications to use the new key). Use key aliases to abstract the key identifiers in applications – this way, you can point the alias to a new key during rotation with minimal application changes. Always plan for key deletion carefully: re-encrypt data with a new key before deleting an old key, and use the maximum waiting period when scheduling deletions to allow time to reverse if needed.
  * **Multi‑Account and Multi‑Region Strategy** – In a multi‑account setup, decide between a centralized vs. decentralized key management model based on operational needs. Many organizations start with a decentralized approach (keys reside in the same account as the data they protect) for simplicity and to avoid cross‑account dependencies. If using a centralized KMS account, implement cross‑account key access carefully with restrictive key policies. For disaster recovery across regions, consider using KMS multi‑Region keys to replicate critical keys to backup regions. Test your DR readiness by ensuring that data encrypted in one region can be decrypted in the secondary region with the replicated key.
  * **Use VPC Endpoints for KMS** – Access AWS KMS through AWS PrivateLink (VPC Interface Endpoints) instead of via the public internet, especially for sensitive or regulated data. By using a KMS VPC endpoint, you ensure that KMS API calls from your VPC stay within AWS’s network and do not traverse the public internet. This reduces exposure to man‑in‑the‑middle risks and helps meet internal network security requirements. Additionally, you can attach endpoint policies to restrict which principals can use the endpoint, adding another layer of control to KMS access.

* **High Availability & Disaster Recovery:**

  * **Regional Redundancy** – AWS KMS is designed to be highly available within an AWS region. It uses multiple redundant HSMs in geographically separate Availability Zones to perform key operations, and it durably stores multiple encrypted copies of keys to achieve 99.999999999% (11 nines) durability for key material. The service is accessible via a regional API endpoint and is backed by an AWS KMS Service Level Agreement for uptime. This in‑region redundancy means KMS operations will continue as long as the region itself is functional, even if an individual HSM or data center goes down.
  * **Multi‑Region Strategy for DR** – To mitigate a full region outage, use KMS multi‑Region keys for critical assets. Multi‑Region keys create interoperable key clones in two or more regions, so encryption in one region can be decrypted in another using the corresponding replica key. In a disaster scenario, your application can fail over to the secondary region and use the replica key to decrypt data. Note that KMS does not automatically fail over – your DR plan must include logic to switch to the backup region’s KMS endpoint and key. If multi‑Region keys are not used, ensure that you have an alternate means to access the data (e.g., maintain an offline copy of the key via CloudHSM or import the same key material into another region’s KMS, if allowed, as part of your DR planning).
  * **Key State and Backup Considerations** – Since KMS keys cannot be exported, traditional “backup” of key material isn’t applicable. Instead, rely on the service’s internal durability and redundancy. For keys with imported material, maintain copies of the original key material in a secure location so you can re‑import if needed. Document and script your key setups (aliases, policies, grants) as Infrastructure‑as‑Code, so that in a worst‑case scenario of losing a region or account, you could recreate keys (though without the original key material, previously encrypted data would remain inaccessible). The emphasis is on prevention: use KMS’s robust durability and avoid accidental deletions rather than expecting to restore keys from backup.

* **Backup & Recovery:**

  * **Scheduling Deletion (Soft Delete)** – AWS KMS does not support an undelete or recycle bin for keys once deleted. Instead, it uses a scheduled deletion mechanism. When you need to decommission a key, you schedule its deletion with a waiting period (7 to 30 days). Use the maximum allowed waiting period (30 days) for any critical key to serve as a soft delete window. This provides a grace period during which you can cancel the deletion if it was a mistake or if you discover the key is still needed. Regularly audit which keys are scheduled for deletion (AWS CloudTrail logs these events) and ensure no key is unexpectedly in queue for removal.
  * **Recovery from Accidental Key Deletion** – If a KMS key is accidentally scheduled for deletion and the error is noticed within the waiting period, you can cancel the deletion to restore the key to active status. However, if a key is deleted after the waiting period, there is no recovery; any data encrypted under that key is permanently inaccessible. Therefore, enforce strict procedures around key deletion: require approval and verification (possibly via an internal change management process) before scheduling deletion of any customer‑managed key. For especially critical data, consider encrypting with multiple layers (e.g., encrypt data with a data key that is itself encrypted by two different KMS keys held in different accounts) so that no single key deletion renders data irrevocably inaccessible – though this adds complexity and is typically only used in highly regulated scenarios.
  * **Alternate Key Recovery Strategies** – In situations where key loss is unacceptable, organizations can use an external key manager or AWS CloudHSM in tandem with KMS. For example, maintain a copy of the master key in a CloudHSM or on‑premise HSM (this would involve using KMS’s custom key store feature or an application‑level encryption strategy). Our environment does not utilize CloudHSM for this purpose, but it’s important to recognize that without such measures, the loss of a KMS key means loss of data. The primary mitigation is to prevent unintended deletions and to utilize key rotation (introducing new keys over time) so that at no point a single key is the sole holder of critical data indefinitely.

* **Access Control & Security Configuration:**

  * **Key Policies and IAM Integration** – Leverage KMS key policies (resource‑based policies on the keys) alongside IAM policies to enforce who can manage keys vs. who can use keys. By default, restrict key administrative actions (like deleting or changing a key policy) to a limited security admin group. Use key policy statements to allow specific AWS principals (IAM roles, users) to use the key for encryption/decryption, and others (or a subset) to manage the key, following least privilege. Regularly review key policies for any wildcards or broad grants that could be tightened. AWS KMS key policies are the primary mechanism for access control and can override IAM permissions, so they must be carefully crafted and tested.
  * **Separation of Duties** – Implement separation of duties for KMS operations: e.g., the team or role that creates and manages keys should be distinct from the application roles that use the keys. This reduces the risk that an application with access to decrypt data could also delete or alter the keys. Use AWS Organizations SCPs (Service Control Policies) if necessary to prevent certain sensitive actions (like disabling or deleting keys) from being performed outside of a break‑glass administrative account.
  * **Encryption Context and Additional Controls** – Encourage the use of the encryption context feature for KMS APIs where applicable. An encryption context is additional authenticated metadata that KMS will bind to the ciphertext; the same context must be provided to decrypt. For example, you might include identifiers like “Application=HRSystem” or “DataClassification=Confidential” as context. This ensures that if ciphertexts or data keys are misused in a different context, decryption will fail, adding an extra layer of protection. Although not all AWS service integrations use encryption context, when building custom applications with the AWS SDK, developers should make use of this feature to strengthen security.
  * **Logging and Alerting** – Enable and forward AWS CloudTrail logs to a centralized logging system or SIEM. Specifically monitor KMS-related events: look for any DisableKey, ScheduleKeyDeletion, or unusual Decrypt patterns. Consider implementing automated responses – for instance, if a highly sensitive key is scheduled for deletion, an automation could immediately cancel the deletion and page the security team. Similarly, use AWS Config to ensure that new keys have rotation enabled and have a proper key policy attached (AWS Config provides managed rules for KMS key rotation and key policy compliance).
  * **Compliance and Key Management** – Align the configuration of KMS with your compliance requirements. For example, if certain data must be encrypted with a key that only your organization controls (for GDPR, PCI DSS, etc.), ensure you are using customer‑managed keys (not AWS‑managed keys) and that access to those keys is audited. Maintain documentation of which KMS keys correspond to which data sets or applications and their classification level. This will facilitate compliance audits and risk assessments. Use AWS’s certification reports (available via AWS Artifact) to verify that AWS KMS meets relevant compliance standards (it is in scope for SOC, PCI, FedRAMP, etc.), and incorporate those into your InfoSec risk analysis for any system using KMS.

## Network Connectivity Options

* **Public Endpoint (AWS KMS Regional API)** – NOT ALLOWED for production use with sensitive data. By default, AWS KMS is accessed via its public regional endpoints (e.g., `kms.eu-west-1.amazonaws.com`) over HTTPS. In our environment, direct Internet access to KMS must be avoided. If an instance or service has no VPC endpoint and must reach KMS, it would require outbound internet access (or NAT gateway) to AWS’s public endpoint. This is disallowed for Restricted data. In rare cases (e.g., development or testing in a constrained environment), if the public endpoint must be used, network egress should be tightly controlled (specific allow‑listed IP ranges or using AWS Private NAT with egress filtering) to limit exposure.
* **VPC Interface Endpoint (AWS PrivateLink)** – Required for most use cases. Deploy an AWS KMS VPC Interface Endpoint (PrivateLink) in each VPC or shared VPC used by our applications. This provides a private IP address for KMS within our network, ensuring all calls to KMS stay on the AWS backbone and do not traverse the public internet. With the endpoint, even services in our on‑prem network (via VPN/Direct Connect) can reach KMS privately. All workloads handling Internal, Confidential, or Restricted data must use the VPC endpoint for KMS. The endpoint can also have a policy that restricts usage to our organization, adding another layer of protection (for example, blocking any calls to KMS for keys that are not ours).
* **Cross‑Region Access** – Avoid designing systems that send KMS traffic across regions over the internet. If multi‑region encryption is needed, use the KMS multi‑Region keys feature rather than having one region call KMS in another region for each encryption operation. Cross‑region KMS calls, if necessary, should ideally also use private connectivity (AWS’s inter‑region private backbones or a multi‑region network architecture) to avoid internet exposure.
* **On‑Premises Access** – When an on‑premise application or a third‑party cloud needs to use our KMS keys (for example, to decrypt data as part of a hybrid solution), route the requests through a secure channel into AWS. This could be accomplished by connecting through a VPN or Direct Connect into a VPC that has a KMS endpoint, or by calling a proxy service in AWS. We do not allow direct calls to KMS from external networks over the open internet for sensitive operations. All such access should be mediated by our controlled network paths and monitored closely.

## Networking & Security Architecture

TBD

## AWS Service Limitations

* **No Key Export or External Use** – AWS KMS does not allow extracting keys for use outside the service. All encryption/decryption must be done by calling the KMS APIs. This means systems outside AWS that need to decrypt AWS‑stored data must either call KMS or have the data keys passed to them (after being decrypted by KMS). There is no “offline” use of KMS keys. This limitation ensures strong control (keys never leave AWS KMS) but can complicate integrations where external systems require the key – in such cases, consider AWS CloudHSM if absolutely necessary (though note that CloudHSM is not approved in our standard catalog).
* **Envelope Encryption for Large Data** – Due to the 4 KB limit on direct encryption, AWS KMS is effectively not suitable for encrypting large payloads directly. Developers must design around this by using envelope encryption. This adds complexity and potential for error (for example, losing an encrypted data key would render data unreadable). It’s crucial to follow AWS’s guidelines or use the AWS Encryption SDK to implement this correctly. This limitation also means KMS is best used for object‑level or record‑level encryption (small chunks, like data keys, tokens, or keys that encrypt files) rather than streaming or big‑file encryption in one go.
* **Latency and Throughput** – KMS calls incur network latency and have throughput limits. If an application were to make KMS Encrypt/Decrypt calls in the critical path of high‑volume transactions (e.g., per user request), it could become a performance bottleneck or hit rate limits. KMS is not optimized for ultra‑low latency, high‑frequency cryptographic operations; it’s intended to manage keys and perform occasional encryption operations. For high‑frequency use cases, one should minimize calls (e.g., cache plaintext data keys in memory for short durations when safe to do so, or batch operations). If truly high TPS encryption is needed, an in‑memory key store or an HSM cluster might be more appropriate (again, outside the scope of standard usage).
* **Integration Gaps** – Not all AWS services or features support every aspect of KMS. For example, as of 2025, some AWS services do not support using asymmetric KMS keys for their encryption features, and certain KMS features (like X.509 certificates in KMS or fully homomorphic encryption) don’t exist. Also, AWS KMS cannot perform bulk cryptographic transformations (like hashing, large file encryption) – it’s focused on key management. These limitations mean that for some needs (such as managing secrets, which is handled by AWS Secrets Manager, or performing SSL/TLS offloading, which might require CloudHSM), other specialized services should be used in tandem. Ensure you choose the right service for the task: KMS for managing encryption keys, CloudHSM if you need direct HSM access, Payment Cryptography for payment‑specific keys, etc., as KMS by itself doesn’t cover every cryptographic use case.

## SKU Features

* **AWS Key Management Service (AWS KMS)** – Fully managed multi‑tenant key management service with HSM‑backed security (keys protected in FIPS 140‑3 Level 3 validated HSMs). Supports symmetric encryption keys, asymmetric key pairs (RSA and ECC), and HMAC keys for cryptographic signing. Integrates with over 100 AWS services for encryption of data at rest and client‑side encryption workflows. All usage is logged to CloudTrail for auditing. It automatically scales with usage; default soft limits (e.g., number of keys per account or requests per second) can be increased by request. Cost: approximately \$1 per key (customer master key) per month plus \$0.03 per 10,000 API calls (20,000 free requests per month).
* **AWS CloudHSM (Not Approved)** – Managed service providing dedicated single‑tenant HSM clusters in your VPC for customers who need exclusive control of keys at the hardware level. AWS CloudHSM uses FIPS 140‑2 Level 3 validated HSM devices and lets you manage and use keys on hardware that you exclusively control. You are responsible for HSM initialization, user management, and high availability configuration. CloudHSM supports a variety of cryptographic operations and custom applications (it provides standard PKCS#11, JCE, and OpenSSL interfaces to the HSM). Because the HSM is under customer control, keys can be exported (in wrapped form) and shared between on‑premises and cloud if needed. Use cases: CloudHSM is typically used for regulatory requirements that demand sole possession of keys or use of proprietary cryptographic algorithms not supported in KMS. Limitations: Higher cost and complexity – you pay hourly per HSM instance (and a one‑time setup fee) and must manage clustering and backups. CloudHSM does not natively integrate with other AWS services (services can’t directly use CloudHSM keys unless you link it via a KMS custom key store). In our environment, CloudHSM and the KMS Custom Key Store feature are not approved for general use (keys must remain in AWS KMS).
* **AWS Payment Cryptography (Not Approved)** – A fully managed cryptography service tailored for payment processing applications, which removes the need for dedicated payment HSMs. It provides payment card industry‑compliant key management and cryptographic operations in the cloud (adhering to PCI PIN, P2PE, and PCI‑DSS standards). AWS Payment Cryptography uses AWS‑managed, PCI‑certified HSM hardware (PCI PTS HSM v3, FIPS 140‑2 Level 3) to perform operations like PIN encryption/decryption, card verification value (CVV/CVV2) generation, EMV ARQC validation, and secure key exchange between parties. It offers separate control plane APIs (to create and manage payment keys, e.g., ZMK, TMK, etc.) and data plane APIs (to execute cryptographic functions like translating PIN blocks or generating card security codes). The service is fully elastic, scaling to provide high throughput and low latency for payment transactions. Use cases: banks, payment processors, and payment networks moving mainframe or on‑prem HSM payment workflows to the cloud. This service is not used in our standard IT environment; it is mentioned for completeness as an alternative specialized solution, but it remains not approved for use in our organization (due to its niche scope and separate compliance requirements).

## Related Service

TBD

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * Information Security Specifications – AWS Key Management Service (AWS KMS)

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:** v.1: 22 Jul 2025 (Dariusz Korzun)

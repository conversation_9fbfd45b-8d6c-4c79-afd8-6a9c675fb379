---
weight: 3
title: "EC2"
date: 2025-07-22
tags: []
summary: ""
---

# Amazon EC2

## Cloud Service Classification

* **Category of the Service:** Compute (Infrastructure as a Service – Virtual Servers)
* **Cloud Provider:** Amazon Web Services (AWS)

## Cloud Service Overview

* **Name:** Amazon Elastic Compute Cloud (Amazon EC2)
* **Description:** Amazon EC2 is a web service that provides on-demand, scalable virtual servers (instances) in the AWS Cloud. It offers resizable compute capacity that lets users launch and manage virtual machines with chosen operating systems (Linux, Windows, etc.) and hardware configurations. EC2 reduces the time and cost of provisioning hardware, allowing applications to scale up or down dynamically to meet demand. Users have full control over their instances’ OS and software, can configure networking and security, and attach storage as needed. EC2 supports a broad range of instance types (over 750) featuring various CPU (Intel, AMD, Arm), memory, storage, and networking options to suit different workloads. Security is built into the platform via the AWS Nitro System, which provides enhanced performance and isolation for EC2 instances.
* **SKU Approval Status:**

  * **PepsiCo Approved:** On-Demand Instances (pay-as-you-go virtual servers with no long-term commitment); Savings Plans (commitment-based discounts on consistent EC2 usage for 1 or 3 years); Reserved Instances (1 or 3-year reserved capacity for specific instance types/Regions at discounted rates); Spot Instances (use spare EC2 capacity at steep discounts, suitable for fault-tolerant workloads); Dedicated Hosts (physical servers dedicated to one customer for compliance or licensing needs); On-Demand Capacity Reservations (reserve capacity in a specific Availability Zone for any duration).
  * **Not Approved:** *None* (All standard EC2 purchase options are available; usage is subject to company guidelines for cost and workload suitability).
* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted
    *(All data classifications are allowed on EC2, but **Restricted** data workloads must adhere to stringent security controls such as no public exposure, encryption, and network isolation.)*

## Service Lifecycle

* **Release Date:** August 25, 2006 (Beta launch; EC2 became generally available with SLA on October 23, 2008).
* **Planned Decommission Date:** No announced.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Elastic Virtual Servers:** Launch and terminate instances on demand, scaling capacity up or down within minutes to match workload needs. EC2 instances provide complete control of the OS and software, similar to running a server in a data center, but with cloud flexibility (e.g., start/stop, API-driven automation).
  * **Instance Variety:** Wide selection of instance *types* and *sizes* offering different combinations of vCPU, memory, storage and network performance. This includes general-purpose instances, compute-optimized, memory-optimized, storage-optimized, and GPU/accelerated instances to support diverse use cases. Users can choose instances with Intel, AMD, or AWS Graviton processors, and even macOS instances for Apple workloads.
  * **Flexible Storage Options:** Integrates with Amazon Elastic Block Store (EBS) for durable block storage volumes attached to instances, and offers *instance store* (local NVMe/SATA SSDs or HDDs) for ephemeral high-speed storage. EBS volumes persist independently of the instance (allowing data to survive instance stops/terminations), whereas instance store is temporary and ideal for cache or buffer data.
  * **Networking & Security:** Instances run in a Virtual Private Cloud (VPC), enabling network isolation and custom subnetting. Security Groups act as stateful firewalls controlling traffic at the instance level. Users can assign Elastic IPs (static public IPv4) to instances and use load balancers and auto scaling for high availability. The AWS Nitro System underlying EC2 enhances security by offloading virtualization to dedicated hardware and minimizing the attack surface. Key pairs (public/private keys) are used for secure SSH/RDP login to instances instead of passwords.
  * **Integration with AWS Ecosystem:** EC2 works with other AWS services: Auto Scaling to adjust instance count based on load, Elastic Load Balancing to distribute traffic across instances, AWS Backup for automated backups, Amazon CloudWatch for monitoring, AWS Systems Manager for patching and automation, and more. In addition, EC2 can be managed via multiple interfaces (AWS Management Console, CLI, SDKs, CloudFormation) for automation and integration into deployment pipelines.

* **Sample Use Cases:**

  * **Web & Application Hosting:** Run scalable web applications, websites, and APIs on EC2 instances. For example, deploy web servers and application servers that can auto-scale across multiple instances to handle variable traffic. EC2’s secure, reliable infrastructure meets demanding enterprise application needs, and instances can be placed in multiple AZs for resilience.
  * **Enterprise Applications & Lift-and-Shift:** Migrate on-premises enterprise workloads (such as ERP systems, custom enterprise apps, or Windows applications) to EC2. EC2’s flexibility allows legacy applications to run in the cloud with minimal changes, while benefiting from on-demand scaling and integration with AWS services.
  * **High-Performance Computing (HPC):** Use EC2 to run compute-intensive simulations and data analysis (e.g., genomics, financial risk modeling, scientific research). HPC-oriented EC2 instances provide high vCPU counts and optionally **Elastic Fabric Adapter (EFA)** networking for low-latency, cluster-style computing. On-demand capacity can be tapped to run HPC jobs faster and cost-effectively without on-premise supercomputers.
  * **Machine Learning & Big Data Analytics:** Train machine learning models or run big data frameworks on EC2 clusters. EC2 offers GPU-accelerated instances for ML training and inference, as well as memory-dense instances for in-memory analytics. These instances provide up to 400 Gbps networking and are purpose-built to optimize price-performance for ML and large-scale data processing. (For example, use GPU instances (P-family) for deep learning model training, or storage-optimized instances (I-family) for Hadoop/Spark data nodes.)
  * **Development and Testing Environments:** Quickly provision disposable dev/test servers. EC2 allows teams to spin up test environments, sandboxes, or continuous integration agents on-demand, and shut them down when not needed. This accelerates development cycles and eliminates the need for maintaining on-prem test hardware. Additionally, EC2 **Mac** instances enable build/test of iOS and macOS applications in the cloud, providing Apple platform development environments on demand.

* **Limitations:**

  * **No Implicit High Availability:** A single EC2 instance is a single point of failure confined to one Availability Zone. EC2 does *not* automatically replicate or failover instances across AZs or Regions. Achieving high availability requires running multiple instances in different AZs behind a load balancer and using Auto Scaling to replace unhealthy nodes. If an AZ outage or instance failure occurs, only applications designed with multi-AZ redundancy will seamlessly fail over.
  * **Resource Quotas:** AWS enforces account-level quotas on EC2 resources. For example, there is a maximum number of vCPUs that can be running on On-Demand instances per region by default. New accounts have conservative limits (e.g., 32 vCPUs for on-demand) which can be increased by submitting a request. Other limits include max Elastic IPs (5 per region by default), security groups per network interface, volumes per instance, etc. Exceeding a quota results in instance launch failures until limits are raised or usage is reduced.
  * **Ephemeral Instance Storage:** Data stored on *instance store* volumes (local disks attached to certain instance types) is **temporary** – it is lost when the instance is stopped, hibernated, or terminated. This makes instance store unsuitable for persistent data. Persistent storage must use EBS volumes or other services (database, S3, etc.). Similarly, instance memory (RAM) is volatile – applications should checkpoint any critical in-memory state to durable storage or enable persistence features for stateful workloads.
  * **No Automatic Backups:** EC2 by itself does not automatically back up instances or data – users are responsible for setting up backups (e.g., EBS snapshots or AMI images). Without a backup strategy, data loss can occur if an instance or volume fails. (AWS provides tools like AWS Backup and Data Lifecycle Manager to automate snapshotting; see **Backup & Recovery** below.)
  * **Specialized Configurations:** Some advanced networking features or instance capabilities require specific instance types or setups. For example, to get 10+ Gbps throughput or EFA network, particular instance families must be used. Also, not all instance types are available in every AWS Region (newer or specialized instances might be limited to certain regions). These constraints may limit choices for certain workloads and require planning (or use of alternate regions) if a needed instance type is unavailable locally.

* **Additional Guardrails:** *TBD*
  *(Enterprise-specific guardrails, such as mandatory tagging, required approval for certain instance sizes, or cost budget alerts, to be defined.)*

* **Used By:** *TBD*
  *(List of some internal applications or teams using EC2, with links to onboarding documentation – to be provided.)*

* **EA Declaration:** NOT a declared standard.
  *(EC2 is broadly used as a foundational service; however, it may not be explicitly declared as a “standard” since it underpins various solutions.)*

## Technical Guidelines

* **Best Practices:**

  * **Security Hardening:** Follow AWS’s shared responsibility model by securing “in the cloud” components. Use IAM roles for EC2 instances instead of embedding API keys (this provides temporary credentials and fine-grained permissions for instance access to other AWS services). Configure the minimum necessary ports/protocols in Security Groups (principle of least privilege) to control inbound access. Regularly patch and update the instance OS and installed software (consider AWS Systems Manager or automated patching solutions). Leverage AWS services like **Amazon Inspector** to scan instances for vulnerabilities and **AWS Security Hub** to continuously audit EC2 against security best practices.
  * **Instance and Resource Management:** Right-size instances for your workload – choose instance types that best match CPU, memory, and network requirements to avoid over-provisioning or bottlenecks. Tag EC2 resources (instances, volumes, etc.) with meaningful metadata (owner, environment, project) for governance and cost tracking. Monitor EC2 service quotas (vCPU limits, EBS volume counts, etc.) and request increases ahead of needs to prevent deployment interruptions. Use AWS Trusted Advisor to get recommendations on underutilized instances, cost savings (e.g., idle instances, rightsizing), and security improvements.
  * **Storage Configuration:** Segregate the OS and application data onto separate EBS volumes when appropriate (e.g., keep database data on a different volume from the root OS). This ensures that data can persist or be managed independently of instance lifecycle (by default, the root EBS volume may be set to delete on instance termination – consider disabling that for critical data volumes). Always enable encryption for EBS volumes, either with AWS-managed keys or customer-managed CMKs, to protect data at rest. If using instance store for caching or high-speed ephemeral data, ensure the application is tolerant of losing that data (or configure replication across instances).
  * **High Availability Design:** Design applications to be stateless or to handle state externally so that you can run multiple interchangeable EC2 instances behind load balancers. Deploy critical components across multiple Availability Zones (at minimum two) for fault tolerance. Use Amazon EC2 Auto Scaling groups to automatically replace unhealthy instances and to scale out/in based on demand. Test instance recovery processes (e.g., how your system behaves when an instance is abruptly terminated) to verify that auto-scaling and failover mechanisms work as expected.
  * **Monitoring and Logging:** Enable detailed monitoring on EC2 (CloudWatch) to track CPU, memory (with CloudWatch agent), disk, and network metrics. Set up CloudWatch Alarms to get notified on critical conditions (e.g., high CPU, low available memory, etc.). Centralize logs from instances (application logs, system logs) using CloudWatch Logs or a log management system for analysis and auditing. Leverage AWS CloudTrail to log all EC2 API calls for security auditing (who started/stopped or modified instances) and set up AWS Config rules to detect undesired configurations (e.g., public IP attached to a Restricted-data instance). Regularly review logs and use Amazon GuardDuty to detect anomalous activities indicating compromised instances (such as unusual IP traffic patterns).
  * **Performance & Cost Optimization:** Use **Optimize CPU** options to disable hyperthreading or reduce vCPU count for licensing-bound or spiky workloads to save on costs. Consider newer generation instances or AWS Graviton-based instances for better price-performance where applicable. For variable workloads, prefer auto-scaling with smaller instance units over one large always-on instance – this can improve resilience and cost efficiency (only run capacity when needed). If instances are underutilized, evaluate moving to a smaller size or using burstable instance types (T-series) for dev/test to reduce cost. For steady workloads, utilize Savings Plans or Reserved Instances to lower cost over time, and use Spot Instances for transient or fault-tolerant tasks to achieve significant cost savings.

* **High Availability & Disaster Recovery:**

  * **In-Region High Availability:** To achieve high availability within a region, deploy EC2 instances across multiple **Availability Zones** (AZs). Use Elastic Load Balancing to distribute incoming traffic across instances in different AZs so that if one AZ goes down, the load balancer will route to instances in the other AZs. Place instances that are part of the same tier (e.g., two web servers) in separate AZs. Use Auto Scaling groups with a multi-AZ configuration so that if an instance in AZ-A fails, the Auto Scaling group can launch a replacement in AZ-B, maintaining the desired capacity. Note that while AZs provide isolation, some failures can still affect an entire region; therefore critical apps might also consider a multi-region strategy.
  * **Backup and Restore (Disaster Recovery within Region):** Ensure that all critical data on EC2 instances is regularly backed up. Use Amazon EBS snapshots for point-in-time backups of volumes and store these snapshots in Amazon S3 (which is multi-AZ by design). For quick recovery of an instance, create an AMI image (which includes snapshots of attached volumes) so you can rapidly launch a clone of the instance if needed. Utilize AWS Backup or AWS Data Lifecycle Manager to schedule regular snapshots and AMI creations, and consider enabling **Crash Consistent** or **Application Consistent** snapshot options for proper state. In case of instance or AZ failure, you can launch a new instance from the latest AMI or volume snapshot in another AZ to restore service.
  * **Multi-Region Disaster Recovery:** For critical systems, plan for region-wide outages by implementing a DR strategy across AWS Regions. This can be **active-passive** (warm standby in a second region) or **active-active**. Regularly **copy EBS snapshots and AMIs to a secondary region** – this ensures you have the machine images and data backups available outside the primary region. Use services like AWS Elastic Disaster Recovery (AWS DRS) for automating cross-region replication of running instances (AWS DRS continuously replicates EC2 block data to another region and can orchestrate failover). For databases, use cross-region replication features (e.g., Amazon RDS read replicas or Aurora Global Database) to keep data in sync. In a failover event, use Amazon Route 53 DNS failover to redirect users to the DR region’s endpoints. Test the cross-region failover process periodically to ensure that launching from copied snapshots/AMIs and switching over via DNS works with minimal downtime.

* **Backup & Recovery:**

  * **EBS Volume Snapshots:** Regularly back up persistent data by taking Amazon EBS snapshots. Snapshots are stored in S3 and are incremental, capturing only changed blocks since the last snapshot, which makes them efficient. You can snapshot volumes while they are in use (crash-consistent), but for critical applications consider quiescing the file system or using application-aware backup techniques. Automate snapshot schedules using AWS Backup or Data Lifecycle Manager to ensure backups are up-to-date. Snapshots can be used to create new volumes or to restore a volume to a previous state (fast restore can be enabled for quicker volume initialization).
  * **AMI (Amazon Machine Image) Creation:** Create AMIs for your instances, especially after configuring a baseline OS and software setup. An AMI is effectively a backup of the instance’s root volume (and any additional volumes you include) plus metadata about instance launch configuration. AMIs enable quick recovery or cloning: if an instance is lost, you can launch a new instance from the AMI in minutes, with the OS and software pre-configured as of the image date. Update AMIs periodically (e.g., after patching or significant changes) so that your “golden image” is current. AMIs can also be copied to other regions to facilitate cross-region recovery.
  * **Database and App Backups:** If your EC2 hosts databases or stateful applications, use application-level backup tools in addition to volume snapshots. For instance, take database dumps or use database native backup to S3 for point-in-time restore beyond what volume snapshots provide. Ensure these backups are also replicated or stored in multiple regions if needed.
  * **Recovery Drills:** Regularly **test the restore process** for your EC2 backups. For example, simulate a server loss by launching a new instance from a snapshot/AMI and verifying that the application comes up and data is intact. Testing validates that your snapshots/AMIs are viable and that you know the steps and time required for restoration. Also test replacing an instance in an Auto Scaling group to ensure the user-data scripts or configuration management re-provision the instance correctly on launch.
  * **Automated Recovery Features:** Consider enabling EC2 Auto-Recovery for critical instances (for supported instance types, you can configure a CloudWatch alarm that triggers AWS to automatically recover or reboot the instance if it becomes impaired due to hardware issues). While this is not a substitute for multi-AZ HA, it can reduce downtime for single-instance failures by automatically moving the instance to healthy hardware. Also use termination protection on critical instances to prevent accidental deletion, and enable **recycle bin** for EBS snapshots to recover from accidental snapshot deletions.

* **Access Control & Security Configuration:**

  * **Identity and Access Management (IAM):** Use IAM roles (instance profiles) to grant your EC2 instances permissions to access other AWS services securely. For example, if an application on EC2 needs to read from S3 or query DynamoDB, assign an IAM role to the instance with only those allowed actions. This avoids storing AWS credentials on the instance and allows key rotation automatically. Manage and audit IAM policies to ensure they are least-privilege for what the instance or users need.
  * **Secure Remote Access:** EC2 uses key pairs for authentication – ensure you **disable password login** on Linux instances (which is the default on AWS AMIs) and use SSH key authentication. For Windows EC2, use the key pair to decrypt the administrator password and then set up strong credentials. Consider using AWS Systems Manager Session Manager for shell access to instances – this allows SSH or PowerShell access through the AWS Console or CLI without opening inbound ports, improving security. Always restrict *who* can SSH/RDP into instances (e.g., via bastion hosts or VPN) and use multi-factor authentication on those entry points if possible.
  * **Network Security:** Configure Security Groups to strictly limit inbound traffic to required ports and sources (for example, only allow TCP 443 from a specific IP range or VPC). Security groups are stateful, so they automatically allow response traffic. Augment security groups with Network ACLs at the subnet level for an additional layer (stateless rules) if needed. For sensitive instances, consider placing them in private subnets with **no direct internet access** – use NAT Gateways or VPC endpoints for any required external connectivity. Implement VPC Flow Logs to monitor network traffic and detect unexpected communication from your instances (which could indicate a compromise).
  * **Encryption:** Ensure that **encryption at rest** is enabled for all EBS volumes containing confidential or restricted data. AWS can automatically encrypt volumes with AWS-managed keys by default, or you can use customer-managed CMKs for more control. For data in transit, configure your application to use encryption protocols (e.g., HTTPS, TLS) for client-server communication. Within AWS, data moving between instances and other services in the same region is often on the AWS private network, but using TLS ensures end-to-end encryption. If using swap or ephemeral storage for sensitive info, consider encrypting those at the OS level as well.
  * **Logging and Monitoring:** Enable AWS CloudTrail for EC2 to record all API calls (e.g., RunInstances, TerminateInstances, ModifySecurityGroup). This provides an audit log of who made changes or launched resources, useful for security forensics. Use AWS Config rules to check for compliance (e.g., flag if an instance is not in an approved subnet or if a security group allows wide-open access). Install and configure anti-malware or host intrusion detection on instances as needed (the AWS Marketplace offers security agents, or use Amazon Inspector which can check for known vulnerabilities and exposures on the instance). Lastly, ensure time synchronization (via Amazon Time Sync Service) is enabled on instances for accurate logging, and aggregate important system logs to a central system for analysis.

* **Network Connectivity Options:**

  * **Public Internet Access:** *Not recommended for sensitive data.* By default, an EC2 instance in a public subnet can be assigned a public IP and communicate over the internet (if an Internet Gateway is attached to the VPC). For **Public** or **Internal** data classifications, public access may be allowed for internet-facing services (e.g., a public web server) but must be tightly controlled using security group rules and, ideally, additional protections like AWS WAF. For **Confidential** or **Restricted** data, direct public internet exposure of EC2 instances is **not allowed** unless a formal security exception is granted. Instead, place instances in private subnets and expose services through approved mechanisms (like behind a reverse proxy or load balancer that performs additional checks).
  * **Private Subnets and VPN:** For internal applications (Internal/Confidential data), host EC2 instances in private subnets with no Internet Gateway route. Access these instances from on-premises or corporate networks via secure tunnels – e.g., AWS Site-to-Site VPN or AWS Direct Connect links into the VPC. This ensures all traffic to the instances is over encrypted private links. Developers or administrators can RDP/SSH into these instances either through a bastion host that itself is tightly secured or by using AWS Systems Manager Session Manager (no direct network exposure).
  * **AWS Direct Connect:** Use Direct Connect for a dedicated, private connection between corporate data centers and AWS for lower latency and higher security. Workloads classified as **Restricted** should use private connectivity (Direct Connect or VPN) when interacting with on-prem systems, rather than traversing the public internet. Direct Connect can be paired with a Transit Gateway to reach multiple VPCs, allowing enterprise network segmentation while keeping traffic private.
  * **VPC Peering / Transit Gateway:** For connecting EC2 instances across different VPCs (for instance, a multi-VPC application or shared services like logging), use VPC Peering or AWS Transit Gateway. VPC Peering is good for small-scale, pairwise VPC connectivity (it’s non-transitive and within a region), while Transit Gateway is a hub-and-spoke model supporting many VPCs and on-premises connections. Both options keep traffic within AWS’s private network. Ensure that security groups and route tables are configured to allow the needed cross-VPC traffic.
  * **VPC Endpoints (PrivateLink):** If EC2 instances need to call AWS managed services (S3, DynamoDB, SQS, etc.), utilize VPC Endpoint interfaces so that those calls stay within the AWS network and do not require an Internet Gateway or NAT. For example, create an S3 Interface Endpoint to allow EC2 to access S3 privately without going out to the public S3 endpoint. This is especially important for Restricted data, to avoid any exposure of data traffic to the internet. Similarly, use AWS PrivateLink to integrate with supported third-party SaaS services privately.
  * **Network Appliances and Proxies:** If additional network security is required (like traffic inspection, URL filtering), deploy network virtual appliances (firewalls) in the VPC and route instance traffic through them (using route tables with appliance as next-hop). Also, for instances that need outbound internet access for patching or API calls but shouldn’t be directly internet-facing, use a NAT Gateway or NAT instance in a public subnet. This allows instances in private subnets to initiate outbound connections (e.g., to download updates) while still not being reachable from the internet inbound.
    *(Note: All network connectivity for **Restricted** data EC2 instances should be through private channels. Any exception requiring public access must go through approval and have proper compensating controls like IP whitelisting, multi-factor auth, etc.)*

* **Networking & Security Architecture:** *TBD*
  *(Diagram and description of a typical network/security architecture for EC2 deployments – e.g., showing VPC, subnets, route tables, firewalls, etc., to be provided by enterprise architecture.)*

* **AWS Service Limitations:**

  * **Instance Limits:** AWS imposes limits on the number of EC2 instances and related resources per account/Region. By default, accounts have a vCPU-based limit for running On-Demand instances (for example, an initial limit might allow up to 32 or 64 vCPUs worth of instances in a Region). Similar limits exist for Spot instance usage and number of Reserved Instances purchases. Ensure to monitor these quotas and plan capacity increases in advance for growth or large deployments.
  * **Networking Limits:** Each network interface (ENI) on an instance can only be attached to a limited number of security groups (default 5 per ENI; up to 16 on some instance types). Also, each security group has a limit of 60 inbound and 60 outbound rules by default (which can be increased to 120 with a request). Designing network rules should take these limits into account (e.g., avoid overly complex rule sets that approach the limits). There are also limits on ENIs per instance (varies by instance size) and IPv4 addresses per ENI. For high loads requiring many IP addresses or security group rules, you may need to distribute across multiple instances or contact AWS to raise limits.
  * **Email (SMTP) Throttling:** By default, EC2 blocks outbound traffic on port 25 (SMTP) to public IP addresses as an anti-spam measure. If your instance needs to send email out directly (e.g., running a mail server or sending application emails via SMTP), you must request AWS to remove this port 25 restriction or use an alternative port/service (such as Amazon SES). Until removed, any attempt to send email on port 25 to external addresses will fail.
  * **Lack of Live Migration:** Unlike some virtualization environments, AWS EC2 does not live-migrate running instances between hosts for maintenance. If the underlying hardware shows degradation or needs maintenance, AWS may reboot or stop/start the instance (with prior notice when possible). This means critical workloads should be architected to handle an occasional reboot or use clustering to avoid single-instance dependency. AWS’s 99.99% SLA for EC2 assumes you have deployed across multiple instances/AZs; a single instance has no uptime guarantee if not part of a redundant setup.
  * **Old Instance Deprecation:** AWS occasionally retires older instance types (“previous generation” instances) as technology advances. While plenty of notice is given, be aware that very old instance families (e.g., T1, M1, C1) are no longer available for new launches and existing ones should be migrated to newer types for better performance and support. Always test and validate new generation instance types for your applications to keep up with AWS enhancements and avoid using deprecated hardware.

* **SKU Features:** *EC2 Instance Families and Capabilities*

  * **General Purpose Instances:** Provide a balanced mix of CPU, memory, and networking resources. Suitable for a wide variety of workloads such as web servers, application servers, and development environments where resource needs are moderate and proportional. *Examples:* T3/T4g burstable instances for low-cost general use; M5/M6 series for consistent performance on typical application tasks. (Ideal for applications using CPU, memory, and network in roughly equal proportions.)
  * **Compute Optimized:** Offer high CPU performance per dollar, ideal for compute-bound tasks. These instances (C5/C6/C7 series) have proportionally more vCPUs and high clock speeds but less memory per CPU. Use cases include batch processing, media transcoding, high-performance web proxies, scientific modeling, and other CPU-intensive workloads. Often used when you need lots of compute power, e.g. running compute clusters for simulations or video encoding.
  * **Memory Optimized:** Provide large memory allotments for memory-bound workloads. Instances like R5/R6, X1/X2, and high-memory (u-series) have high RAM-to-vCPU ratios. They are tailored for in-memory databases (e.g., Redis, SAP HANA), big data analytics in memory, caching servers, or high-performance databases that rely on large memory pools. Use these when the dataset or workload primarily resides in memory and needs fast memory access.
  * **Accelerated Computing (GPU/FPGA):** Equipped with hardware accelerators such as GPUs or FPGAs for specialized computations. GPU instances (P-family for NVIDIA Tesla GPUs, G-family for graphics/compute) are used for machine learning training, inferencing at scale, video rendering, and graphics-intensive applications. FPGA-based instances (e.g., F1) allow custom hardware acceleration for genomics, encryption, or real-time analytics algorithms. Also included are AWS Trainium (Trn1/Trn2) and Inferentia (Inf1/Inf2) instances for ML workloads – these offer high performance at lower cost for AI applications.
  * **Storage Optimized:** Feature high-throughput, low-latency local storage (HDD or NVMe SSD) directly attached to the instance, along with high IOPS capability. Examples are I3/I4 (NVMe SSD-backed) for high random I/O (suitable for NoSQL databases, OLTP databases, Kafka clusters) and D2/D3 or H1 instances (with HDDs) for high sequential throughput (big data, Hadoop, data warehousing). These instances are designed to deliver millions of IOPS or hundreds of MB/s of throughput from local disks to the application. Data on instance stores persists only during the instance’s life, so these are best for distributed data stores that replicate or where data is reproducible from elsewhere.
  * **HPC Optimized:** Purpose-built for High Performance Computing at scale on AWS. Hpc-series instances (e.g., Hpc6a, Hpc7g) offer the best price-performance for tightly coupled compute clusters, with features like **Elastic Fabric Adapter (EFA)** networking for ultra-low latency, high throughput internode communication. They often have no hyperthreading (1 thread per core for consistency) and high core counts. These are ideal for computational fluid dynamics, weather modeling, finite element analysis, and other MPI or GPU-cluster workloads that require near-supercomputer capabilities.
    *(When choosing an EC2 instance, select the family that best matches the workload profile to maximize performance and cost efficiency. Each family has multiple sizes (e.g., large, xlarge, etc.) to scale resources as needed.)*

* **Related Service:** *TBD*
  *(Placeholder for related AWS services that work closely with EC2, e.g., Amazon ECS/EKS for containers, AWS Lambda for serverless, etc.)*

* **Alternatives:**

  * **AWS Fargate / Container Services:** Instead of managing servers directly on EC2, you can use AWS container services. Amazon ECS (Elastic Container Service) and EKS (Elastic Kubernetes Service) allow running Docker containers on clusters of EC2 instances with AWS handling much of the cluster management. With AWS Fargate, you can run containers without managing any EC2 instances at all (serverless containers). These options abstract the infrastructure, which can simplify operations for microservices and containerized workloads.
  * **AWS Lambda (Serverless):** For event-driven or intermittent workloads, Lambda functions let you run code without provisioning or managing servers. If you can architect your application into small functions that execute on demand (for example, an API endpoint or data processing trigger), Lambda removes the need for an always-on EC2 instance and automatically scales with demand. This is an alternative when the workload fits into stateless, short-duration execution models.
  * **Amazon Lightsail:** Lightsail is a simplified service for small-scale deployments, offering pre-configured virtual private servers, databases, and load balancers with a predictable monthly pricing model. It uses EC2 under the hood but abstracts networking and instance configuration details. This is suitable for simple websites, blogs, or prototypes that might otherwise use a single EC2 instance, providing ease of use and bundled resources (compute, storage, static IP).
  * **Managed AWS Services:** In some cases, using a higher-level managed service can replace running software on EC2. For example, instead of running a relational database on EC2, one could use Amazon RDS; instead of running Hadoop on EC2, use Amazon EMR; instead of hosting an in-memory cache on EC2, use Amazon ElastiCache. These managed services reduce operational burden and automatically handle tasks (like backups or patching) that you would need to do manually on EC2.
  * **On-Premises or Hybrid (AWS Outposts):** For scenarios where workloads need to remain on-premises (due to latency or data residency) but you want a cloud-consistent experience, AWS Outposts can be an alternative. Outposts uses the same EC2 technology in your own data center, managed by AWS. This isn’t an alternative *service* to EC2 so much as an alternative deployment model, but it can replace the need to run separate virtualization stacks on-prem; you manage EC2 instances on Outposts just like in AWS cloud, for a hybrid solution.

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * *Information Security Specifications – Amazon EC2* (Refer to PepsiCo’s internal InfoSec baseline document for AWS EC2, which covers required security configurations, hardening standards, and compliance controls for EC2 instances.)
  * *Compliance Certifications:* Amazon EC2 is part of AWS’s compliant infrastructure for a variety of standards. Notably, EC2 has been validated for **PCI DSS Level 1** compliance, meaning it can be used to process and store credit card data in a compliant manner. EC2 is also in scope for SOC 1, SOC 2, ISO 27001, FedRAMP, HIPAA, and other frameworks under AWS’s compliance programs. Customers should ensure their use of EC2 aligns with these standards (e.g., implementing required encryption and access controls) and can obtain relevant audit reports (PCI Attestation, SOC reports) from AWS Artifact for their compliance needs.

*(Ensure all EC2 deployments follow PepsiCo’s InfoSec policies – including hardened AMIs, disabling unnecessary services, regular patching, least privilege IAM roles, network segmentation, and monitoring – to maintain compliance and security posture.)*

## Ownership and Version Control

* **Service Architect:** John Doe ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * *v.1:* 22 Jul 2025 – Initial draft prepared for Amazon EC2 service catalog item (John Doe)

---
weight: 4
title: "S3"
date: 2025-07-22
tags: []
summary: ""
---

# Amazon S3 (Simple Storage Service)

## Cloud Service Classification

* **Category of the Service:** Storage
* **Cloud Provider:** Amazon Web Services (AWS)

## Cloud Service Overview

* **Name:** Amazon Simple Storage Service (Amazon S3)
* **Description:** Amazon S3 is a scalable **object storage** service that allows storing and retrieving any amount of data from anywhere on the web. It offers industry-leading durability (designed for 99.999999999% data durability) and high availability (99.99% by default) by redundantly storing data across multiple facilities. S3 provides **virtually unlimited capacity**, high performance, and fine-grained security controls. Organizations use S3 to **store, manage, and protect** data for a wide range of use cases – from data lakes and analytics to application content storage and backups – with cost-effective storage classes and easy-to-use management features for organizing data and controlling access.
* **SKU Approval Status:**

  * **PepsiCo Approved:** *TBD*
  * **Not Approved:** *TBD*
  * *S3 Standard* – TBD
  * *S3 Intelligent-Tiering* – TBD
  * *S3 Standard-IA* – TBD
  * *S3 One Zone-IA* – TBD
  * *S3 Glacier Instant Retrieval* – TBD
  * *S3 Glacier Flexible Retrieval* – TBD
  * *S3 Glacier Deep Archive* – TBD
  * *S3 Express One Zone* – TBD
  * *S3 on Outposts* – TBD
* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date:** March 14, 2006
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Durable, Scalable Object Storage:** Amazon S3 stores data as objects within buckets, providing *massively scalable* storage for any type of data. It is designed for **11 nines** of durability (99.999999999%) and redundantly stores objects across a minimum of three Availability Zones in a region (for Standard and most other classes). This multi-AZ design ensures that data remains intact even if an entire data center becomes unavailable. S3 also delivers high throughput and low latency, supporting a very large number of concurrent read/write operations, and **automatically scales** to meet demand without provisioning capacity.
  * **Flexible Storage Classes:** S3 offers multiple storage classes to optimize cost and performance for different data access patterns. These include S3 Standard for frequent access, S3 Standard-IA and One Zone-IA for infrequent access, S3 Intelligent-Tiering which automatically moves data between tiers based on usage, and archival classes like S3 Glacier Instant Retrieval, Glacier Flexible Retrieval, and Glacier Deep Archive for long-term retention at lower costs. Each class provides different price points and retrieval characteristics while maintaining the same 11×9 durability (except One Zone classes trade AZ redundancy for cost). Lifecycle policies can automate moving objects to cheaper tiers or deleting them as they age.
  * **Security and Access Management:** S3 provides robust security features. All new buckets have **server-side encryption enabled by default**, ensuring data at rest is encrypted (AES-256 by SSE-S3) without user intervention. For sensitive data, you can use AWS Key Management Service (SSE-KMS) for envelope encryption or even customer-provided keys (SSE-C). Access to buckets and objects is private by default and controlled via IAM policies and bucket policies; fine-grained permissions can be set at the resource level. S3 supports **Bucket Policies** (resource-based JSON policies) and **Access Control Lists**, though AWS recommends disabling ACLs and using bucket owner enforced settings to manage access exclusively via policies. Features like **S3 Block Public Access** help prevent accidental exposure by blocking any public ACL or policy settings. For audit and compliance, S3 integrates with AWS CloudTrail and can produce access logs for every request.
  * **Data Management Features:** S3 includes capabilities to manage data lifecycle and integrity. **Versioning** can be enabled on buckets to keep multiple versions of an object, allowing easy recovery from accidental deletions or overwrites. **S3 Object Lock** provides WORM (Write-Once-Read-Many) functionality to prevent object deletions or modifications for a specified retention period, aiding in compliance and ransomware protection. **Object tagging** enables key-value tags on objects for organizing data and controlling lifecycle or access. S3 also supports **Cross-Region Replication (CRR)** to automatically copy objects to a bucket in another AWS region for disaster recovery or data locality needs. **Multipart upload** capability allows efficient upload of large objects in parts (required for files over 5 GB) and improves resiliency of uploads over unreliable networks.
  * **Event-Driven Integrations:** S3 is deeply integrated with other AWS services. It can publish **event notifications** to AWS Lambda, Amazon SNS, or Amazon SQS when certain events occur (such as object created or deleted), enabling serverless processing pipelines and workflows triggered by data changes. S3 also supports **S3 Object Lambda**, allowing you to intercept and process data (e.g., filter, transform, or redact) on-the-fly when an object is retrieved. Additionally, S3 can function as static website hosting for public content, and it integrates with Amazon CloudFront CDN to efficiently distribute content globally. S3’s **Select** and **Glacier Select** features even allow running SQL-like queries directly against data in objects (like CSV/JSON) or in archived data without fully downloading the objects.

* **Sample Use Cases:**

  * **Data Lake & Analytics:** Use S3 as a central data lake to store raw and processed datasets (logs, clickstreams, IoT data, etc.) at petabyte scale. Analytical services such as Amazon Athena, EMR, Redshift Spectrum, or machine learning training jobs can directly **read data from S3**. S3’s high throughput and parallelism allow big data processing and AI/ML workloads to efficiently access large volumes of data in place. For example, companies build enterprise data lakes on S3 to enable organization-wide analytics and insights.
  * **Backup and Disaster Recovery:** S3 is ideal for backup storage due to its durability and scalability. Databases and file systems can back up snapshots or data dumps to S3 (e.g., nightly backups of on-prem databases or EBS snapshots copied to S3). Using **S3 Glacier** tiers, long-term archives of backups can be maintained at low cost. In DR scenarios, critical data in S3 can be replicated to another region (using CRR or AWS Backup) so that if one region is down, data is available in the backup region. For instance, AWS Backup service now supports continuous backups of S3 (with point-in-time recovery up to 35 days) and cross-region copies of backups for resiliency.
  * **Archiving and Compliance Storage:** Organizations leverage S3 for **cold data archival** – storing infrequently accessed information such as compliance records, historical logs, transaction records, or media archives. By transitioning old data to **S3 Glacier Instant Retrieval, Glacier Flexible Retrieval, or Deep Archive**, they drastically cut storage costs while still having the data accessible when needed (Glacier Instant Retrieval provides milliseconds access for archives that might need quick retrieval like medical images, whereas Deep Archive is used for data that can afford hours of retrieval time). S3 Object Lock (in compliance mode) can be used on archive buckets to satisfy write-once-read-many regulatory requirements (preventing deletion for a specified retention).
  * **Application File Storage & Content Distribution:** S3 can act as a persistent storage layer for web and mobile applications. Examples include storing user-uploaded files (images, videos, documents), media assets for a website, or large data exports for later download. Applications can offload these static files to S3 to reduce load on application servers. When coupled with Amazon CloudFront (CDN), content from S3 (like website images, videos, or software downloads) can be delivered globally with low latency. S3’s static website hosting feature can even serve simple websites or act as the origin for web content. In internal use, teams might use S3 to share data files across departments or as a drop zone for data exchange between systems.
    *(Additional use cases to be added as identified.)*

* **Limitations:**

  * **Object Size Limits:** Individual S3 objects can range from 0 bytes up to **5 TB** in size. The largest object that can be uploaded in a single PUT operation, however, is 5 GB – for anything larger, the multipart upload API must be used. This means very large files need to be split into parts for upload, and clients should be designed to handle multipart uploads if dealing with files >5 GB.
  * **Eventual Consistency in Cross-Region Scenarios:** Within a single region, Amazon S3 now provides strong read-after-write consistency for all operations (GET or LIST after a PUT will immediately reflect the new data). However, if using **Cross-Region Replication**, note that replication is **asynchronous**. There can be a lag (typically seconds or minutes) before updates in the source bucket appear in the replica bucket. In the unlikely event of a primary region failure, recently written objects not yet replicated could be lost. Applications with multi-region deployments must account for this replication delay.
  * **Minimum Storage Duration and Retrieval Fees:** Some S3 storage classes have minimum storage duration charges and retrieval costs. For example, S3 Standard-IA and One Zone-IA have a **30-day minimum** storage charge, and S3 Glacier Flexible Retrieval has a 90-day minimum, while S3 Glacier Deep Archive requires 180 days. If an object is deleted or moved to a warmer tier before that minimum age, AWS still bills for the full minimum duration. Archived storage classes (Glacier) also impose data retrieval fees (per GB) and retrieval latency (minutes or hours for non-instant tiers), which should be factored into use cases – frequent or urgent data retrievals from Glacier classes can incur costs or delays.
  * **Throughput Throttling at Extreme Scale:** S3 can handle very high request rates and will **automatically scale** to meet throughput demands. There are no fixed limits on TPS, but a single partition (prefix) of a bucket can sustain *at least* **3,500 PUT/DELETE** requests per second and **5,500 GET** requests per second by default. S3 will scale beyond these rates by internally partitioning buckets based on object key prefixes. If an application suddenly greatly exceeds these rates on one prefix, S3 may respond with HTTP 503 “Slow Down” errors while it scales up behind the scenes. To avoid this, distribute load across different key prefixes (which S3 now does automatically in most cases) or implement retry with backoff on 503 errors. Hotspotting on a single object (e.g., thousands of requests per second to *one* object) can also become a bottleneck – in such cases, caching layers (CloudFront or ElastiCache) should be considered.
  * **Atomicity and Indexing Limitations:** S3 is an eventually consistent object store and does not support multi-object transactions or complex queries natively. Operations are atomic per single object, but there is no mechanism for atomic updates across multiple objects (no distributed transactions). Also, S3 has no built-in indexing or querying of object contents – you cannot natively query by object metadata beyond prefix and tags. To search or query data in S3, you must use external services like Amazon Athena (for SQL on object data), AWS Glue Catalog (for metadata indexing), or Amazon OpenSearch (to index object metadata or contents). This means applications requiring relational querying or real-time random access to subsets of data might need to use a database or another storage solution on top of or in addition to S3.

* **Additional Guardrails:** *TBD*

* **Used By:** *TBD (list & links to some internal onboardings using S3)*

* **EA Declaration:** *NOT a declared standard.*

## Technical Guidelines

* **Best Practices:**

  * **Secure Bucket Configuration:** Ensure that **public access is blocked** for all S3 buckets unless explicitly required for a use case (and even then, only data classified as Public should be in any publicly accessible bucket). By default, S3 buckets block public access; retain these settings and use bucket policies to tightly restrict access. Avoid using bucket ACLs for access control (keep them disabled via Object Ownership) and instead manage permissions with IAM roles and bucket policies (principle of least privilege). Regularly audit bucket policies for any overly broad access (e.g., wildcard principals).
  * **Encryption & Data Protection:** Leverage S3’s encryption features to protect data at rest and in transit. **Always use HTTPS** for connections to S3 endpoints – enforce this by requiring the `aws:SecureTransport` condition in bucket policies to deny any HTTP requests. For sensitive or Confidential/Restricted data, use **Server-Side Encryption with AWS KMS** (SSE-KMS) so that you have control over the encryption keys and can set up key rotation policies. SSE-KMS also allows enabling of features like separate permissions for decrypting data. If applicable, enable **Dual-layer encryption (DSSE-KMS)** for highly sensitive data, which encrypts data with two independent KMS keys. On the client side, do not store or transmit unencrypted sensitive data—if client-side encryption is used, ensure proper key management outside AWS.
  * **Versioning and Object Lifecycle:** **Enable Versioning** on buckets that contain critical data or long-lived data sets. Versioning provides an easy recovery path from accidental deletions or overwrites by preserving older versions of objects. In combination with MFA Delete (which requires additional authentication for delete operations on versioned buckets), this significantly mitigates the risk of data loss. Establish clear **Lifecycle policies** to manage data growth and costs: for instance, automatically transition objects to cheaper storage classes as they age (e.g., move to Standard-IA after 30 days of no access, then to Glacier after 90 days) and expire (delete) objects that are no longer needed after a certain retention period. Lifecycle rules help enforce data retention requirements and cost optimization without manual intervention.
  * **Monitoring and Auditing:** Implement monitoring on S3 activities to detect any abnormal access patterns or security issues. **CloudTrail Data Events** for S3 should be enabled to log object-level operations (GET, PUT, DELETE) for audit purposes. Configure Amazon **CloudWatch Alarms** on relevant metrics (e.g., a sudden spike in `NumberOfObjects` or `BytesDownloaded` that could indicate unexpected usage). Enable **Server Access Logging** on buckets (to a separate logging bucket) to have a record of every access request for deeper analysis. Use Amazon S3 **Storage Lens** for organization-wide visibility into storage usage and activity trends. Additionally, consider using Amazon Macie for data loss prevention (it can scan S3 buckets for sensitive information). Regularly review access logs and utilize AWS Config rules (like `s3-bucket-public-read-prohibited`, `s3-bucket-encryption-enabled`, etc.) to continuously audit compliance of buckets with security best practices.
  * **Cost Optimization:** Take advantage of the various storage classes and features to minimize cost. Use **S3 Intelligent-Tiering** for data sets with unknown or unpredictable access patterns – it will automatically tier objects to infrequent access or archive tiers to reduce cost when data isn’t accessed, without any performance impact on retrieval. For known patterns, move data manually via lifecycle policies (e.g., to Glacier Deep Archive for data that must be retained for years but likely won’t be accessed). Delete or archive old versions of objects if versioning is enabled and they are no longer needed (you can apply lifecycle rules to non-current versions). **Compress data** before uploading when applicable (for example, text or CSV files can be gzipped) to save space and transfer time. Monitor your S3 storage costs and use AWS Budgets or Cost Explorer to find anomalies (S3 Storage Lens can also show cost-related metrics). Also consider **data partitioning practices** (by date, category, etc. in object keys) that allow you to delete or transition only a subset of data easily when it expires, rather than mingling hot and cold data in the same prefix.
  * **Performance Optimization:** For workloads requiring high throughput or low latency, **distribute reads/writes across keys or prefixes**. S3 will handle scaling automatically, but ensure your key naming avoids sequential prefixes for high-speed inserts (to prevent initial hotspots). If you have extremely latency-sensitive requirements (single-digit millisecond access), consider using **S3 Express One Zone** storage class in conjunction with co-locating compute in the same AZ for that data. Use **Multipart Upload** for uploading large files in parallel, and use **Byte-Range GETs** or parallel downloads for faster retrieval of large objects. If access patterns involve a lot of small, frequent reads, caching the data in memory or using a CDN (CloudFront) in front of S3 can drastically improve performance. Finally, test your workload with representative data and use S3’s **performance metrics** (through CloudWatch or Storage Lens) to identify any bottlenecks.

* **High Availability & Disaster Recovery:**

  * **Regional Redundancy:** S3 is inherently a highly available service within a region. For standard storage classes, data is stored redundantly across at least three AZs, meaning an AZ outage will not interrupt S3 operations or cause data loss. Applications can generally rely on S3’s regional durability and availability (S3 Standard provides 99.99% availability and has a Service Level Agreement of 99.9% uptime) for HA within the region. No special configuration is needed to benefit from this multi-AZ resilience.
  * **Cross-Region Replication:** For Disaster Recovery (DR) across AWS regions, configure **S3 Cross-Region Replication** on critical buckets. This will asynchronously replicate new objects (and optionally delete markers/version edits) from a source bucket to a destination bucket in another region. In a DR scenario where the primary region is unavailable for an extended period, the data in the replica bucket can be used to restore application functionality. Note that failover is not automatic at the S3 service level – the application or operations team must redirect workloads to the replica bucket/region. It’s important to enable Versioning on both source and destination buckets when using CRR (a requirement for replication). Also consider enabling **Replication Time Control** (an S3 feature that provides an SLA on replication latency, e.g., 99.99% of objects replicated within 15 minutes) for more predictable DR outcomes.
  * **Multi-Region Access Points:** For active-active architectures or simplified multi-region access, AWS offers **S3 Multi-Region Access Points**, which provide a single global endpoint that routes S3 requests to multiple buckets in different regions. With this setup, S3 can automatically route requests to the nearest healthy region. In case of a regional disruption, it will fail over to an alternate region’s bucket if configured. New *failover controls* allow you to manually designate an active vs. standby region and trigger failover within minutes in the event you need to redirect all traffic to a backup region. Multi-Region Access Points (combined with CRR) can greatly simplify building multi-region redundant applications without custom routing logic.
  * **Backup and External Recovery:** In addition to replication, consider **periodic backups of S3 data** using AWS Backup or other backup tools. AWS Backup can do point-in-time backups of S3 buckets (with continuous backup allowing restore to any past point in last 35 days) and store backups in a Backup Vault, including in different regions or accounts for extra isolation. This protects against not only regional failure but also accidental deletions or malicious actions (if the backup vault is separately secured). It’s also a defense against a compromised AWS account scenario, when cross-account backups are used. Define Recovery Time Objectives (RTO) and Recovery Point Objectives (RPO) for your data to determine if standard CRR is sufficient or if you need additional mechanisms. For example, an RPO of near-zero might require synchronous replication at the application layer or using storage solutions like database replication in addition to S3. Finally, periodically **test your DR plan**: for example, simulate failover by switching your application to read from the replica bucket, or restore a sample of data from AWS Backup to verify integrity.

* **Backup & Recovery:**

  * **Versioning and Restoration:** If S3 Versioning is enabled, recovering from accidental deletions or modifications is straightforward. A deleted object (with versioning on) just creates a delete marker while previous versions remain intact; you can **“undelete”** by either retrieving the prior version or deleting the delete marker. Likewise, if data is corrupted or incorrectly updated, an older version can be restored. It’s a best practice to enable versioning on buckets that hold mission-critical or irreplaceable data so you have this safety net. For additional protection, use **MFA Delete** on those buckets to require multi-factor authentication for deletion of versioned objects or to suspend versioning (this helps prevent an attacker or operator without MFA from permanently removing data).
  * **S3 Object Lock for WORM:** Enforce retention policies using **S3 Object Lock** in Compliance Mode for regulatory data or backups that should not be deletable within a certain timeframe. With Object Lock, you can set retention periods or legal holds on objects; during that time, S3 will reject any delete or overwrite of those objects. This is useful for protecting backups from ransomware or malicious deletion – even an admin cannot delete locked data until the retention period expires. Design backup jobs to optionally use a retention lock (for example, keep daily backups with a 30-day lock).
  * **AWS Backup Integration:** Leverage **AWS Backup** to manage backups of S3 at scale. AWS Backup can create immutable, point-in-time snapshots of S3 buckets (it requires versioning to be enabled) and store them in a centralized backup vault. You can configure continuous backups (allowing point-in-time recovery to any moment in last 35 days) or periodic backups on a schedule. AWS Backup supports cross-account and cross-region backup copies, which is valuable for ensuring recoverability even if the primary account is compromised or the region has an issue. Restoring from AWS Backup can recover either the entire bucket or selected objects to a point in time. Incorporate AWS Backup into your data protection strategy, especially for buckets that contain critical state (database backups, important documents, etc.).
  * **Cross-Region and Offline Backups:** In addition to CRR which keeps a near-real-time replica, you may implement periodic **cross-region backups** (e.g., once a day copy) for simpler point-in-time DR. This could be done with replication rules or manually via batch jobs that copy data to a backup bucket in another region (and possibly in another account). For extremely critical data, some organizations also keep an **offline backup**: for example, using AWS Snowball or Snowcone to export data from S3 to an offline media on a schedule, or use third-party tools to sync S3 data to an on-premises storage for cold backup. While S3’s durability is extremely high, having an offline copy provides ultimate insurance against rare events and can meet certain compliance rules (like keeping a copy of data entirely off-cloud).
  * **Recovery Procedures:** Document and practice the procedures to restore data. For versioned buckets, train teams on how to list object versions and retrieve or delete markers to recover prior versions. For AWS Backup, ensure that administrators know how to locate backups in the AWS Backup console or via CLI and initiate a restore job (restoring either to the original bucket or an alternate location for validation). If using CRR or multi-region setups, determine how the application will **failover** to the replica – this might involve updating config to point to the new bucket, or using Multi-Region Access Points as described in HA section. Regularly verify that backups and replicas are working (e.g., the replication lag is within acceptable range, backup jobs succeed, and sample data is recoverable). Implementing **object integrity checks** (like storing checksums and validating them on restore) is also recommended to ensure the recovered data is uncorrupted.

* **Access Control & Security Configuration:**

  * **Identity & Access Management (IAM):** Use IAM roles and policies to manage access to S3 resources instead of hard-coding credentials. For example, an EC2 instance or AWS Lambda function that needs to access a bucket should use an **IAM Role** with the necessary S3 permissions attached, rather than an AWS access key. This leverages temporary security credentials and avoids long-term keys that could leak. Define fine-grained IAM policies that grant least privilege – e.g., an application that only needs to read from a specific bucket should be given an IAM policy with `s3:GetObject` on that bucket (and possibly narrowed by prefix). Regularly audit IAM roles/users for overly broad S3 permissions (especially any wildcards on S3 actions or resources) and refine them.
  * **Bucket Policies and ACLs:** Implement **Bucket Policies** to enforce access rules at the bucket level for all requests. For instance, you can restrict a bucket to be accessed only from your corporate VPC or only by your AWS Accounts. Use conditions in bucket policies (such as IP address whitelist or `aws:PrincipalOrgID` to allow only accounts from your AWS Organization). *Do not rely on ACLs:* as a best practice, **disable ACLs** by setting the bucket’s Object Ownership to “Bucket owner enforced” (this is now the default on new buckets). With ACLs off, all objects are owned by the bucket owner and access must come via policies, simplifying governance. If you have a use case for cross-account object ownership (uncommon), consider S3 Access Points or bucket policy trust rather than ACLs.
  * **Block Public Access & Organization Controls:** Ensure **Block Public Access** is enabled at the bucket and even at the account level for all environments except where public buckets are absolutely required. This feature overrides any attempt to make a bucket or object public. In an enterprise setting, it’s recommended to keep all buckets private and only use CloudFront or other mechanisms to expose content if needed. You can also use AWS Organizations SCPs (Service Control Policies) to prevent creation of public buckets or to enforce encryption. For example, an SCP can deny any S3 operation that doesn’t include encryption headers or that targets a bucket without a specific tag, etc. Use AWS Config rules and Security Hub to continuously check S3 settings (like public access, encryption, versioning).
  * **Encryption Configuration:** As mentioned, all buckets should have default **encryption at rest** enabled (SSE-S3 by default encrypts with AES-256 and AWS-managed keys). For buckets hosting sensitive data, consider requiring **SSE-KMS** encryption so that objects are encrypted with a Customer Master Key in AWS KMS; you can enforce this by a bucket policy that denies any put object which doesn’t have the `"x-amz-server-side-encryption":"aws:kms"` header or a specific KMS key ID. Manage KMS keys carefully – limit who can use them to decrypt data and watch the AWS CloudTrail for KMS usage. If using SSE-KMS, be aware of the KMS **limits**: by default KMS supports a certain number of requests per second (for example, about 5,500 encrypt/decrypt per second per key in KMS). If you anticipate a very high volume of small object writes with KMS encryption, you may need to request a quota increase or use a data key caching library to reduce calls to KMS. Data in transit to S3 should always be encrypted via SSL/TLS; the AWS CLI and SDKs use HTTPS by default – ensure no clients override this.
  * **Network Access Controls:** For additional security, especially for **Restricted** data, access S3 via **VPC Endpoints** rather than over the public internet. An **S3 VPC Gateway Endpoint** allows EC2 instances in a VPC to privately connect to S3 without an Internet Gateway or NAT, using the AWS backbone network. This also enables you to restrict bucket access to only come from your VPC – for example, a bucket policy can allow `Principal:*` but with condition `aws:SourceVpce` (specific VPC endpoint ID) or `aws:SourceVpc` to only accept traffic via your VPC endpoint. Such a policy effectively **prevents any access from outside the VPC**, even if credentials are compromised. If on-premises systems need to access S3 privately, you can use AWS PrivateLink by setting up an **Interface VPC Endpoint** for S3 (this provides a private IP address endpoint that on-prem can reach via Direct Connect or VPN). Note that for S3, Gateway Endpoints are generally cheaper and more performant for in-region access, but Interface Endpoints might be used in cross-region or specific network scenarios. Use **VPC Endpoint policies** to further restrict what can be done via the endpoint (for instance, only allow access to certain buckets).
  * **Logging and Alerting:** Configure **access logs** on critical buckets by enabling server access logging to a dedicated logging bucket (with proper security on that bucket to avoid exposure of logs). These logs can be processed by tooling or Amazon Athena to analyze access patterns. Use Amazon **CloudWatch Events / EventBridge** in conjunction with S3 (or CloudTrail) to trigger alerts – e.g., if a bucket policy is changed, or if an ACL is set to public, or if there’s a burst in GetObject requests late at night, trigger an alert or Lambda function for investigation. Services like **Amazon GuardDuty** also monitor CloudTrail and VPC Flow Logs for suspicious S3 access (GuardDuty has specific S3 threat detections, such as anomalous data retrieval patterns). Enabling GuardDuty for S3 is recommended for an additional layer of intelligent threat detection.

* **Network Connectivity Options:**

  * **Public Endpoint (Internet):** *Not Allowed for sensitive data.* By default, S3 buckets are accessible through the public S3 endpoints on the internet (with proper credentials). In the PepsiCo environment, direct access to S3 over the public internet should be avoided for Internal, Confidential, or Restricted data. If a bucket must host public content (data classified as Public), it should be carefully reviewed and approved, and locked down to serve only the intended content (for example, static website assets). In such cases, enable Block Public Access on the bucket and use bucket policies to explicitly allow read-only access to specific objects or prefixes for the public (or better, use CloudFront to front the bucket). Any **public-facing S3 content requires approval** and must not contain sensitive information.
  * **AWS VPC Gateway Endpoints (Private Access):** **Preferred method** for internal connectivity. Use S3 Gateway Endpoints to route all S3 traffic from within a VPC through the AWS private network. Gateway Endpoints are cost-free and highly scalable, and you can attach them to your route tables so that S3 traffic does not require an Internet Gateway or NAT. When using a Gateway Endpoint, configure the bucket policy to allow access only from your VPC (this provides a *logical private link* to your buckets). This ensures that even if credentials are leaked, the bucket cannot be accessed from outside the corporate VPC environment. All PepsiCo AWS accounts and VPCs that interact with S3 should have the S3 Endpoint enabled by default.
  * **AWS VPC Interface Endpoints (AWS PrivateLink):** *Optional in special scenarios.* An Interface Endpoint for S3 exposes a private IP in your subnet that forwards to S3. This is typically not needed for S3 within the same region (Gateway Endpoints cover that). However, if you need to access S3 from on-premises through a private connection like Direct Connect without traversing the Internet, an Interface Endpoint in combination with DX can be used. Interface Endpoints incur hourly and data processing charges, so use them only when required. They may also be used if you want to restrict access to *specific* S3 API operations or need to use VPC endpoint policies for fine-grained control (since Gateway endpoints have more limited policy support). Generally, for most use cases, prefer Gateway Endpoints for S3 due to their simplicity and no cost.
  * **S3 Access Points:** Consider using **Amazon S3 Access Points** for large-scale data sets or multi-tenant environments. An Access Point is essentially an alias to a bucket with its own policy and network controls. For example, you can create an Access Point that is restricted to a specific VPC and namespace (for a particular application or team) – making it easier to manage access for that use case without affecting the bucket’s main policy. Access Points can also simplify managing permission for shared datasets (each consumer can get an access point with tailored rights). Use-case: a central data lake bucket might have multiple access points: some only accessible from certain VPCs, others allowing only read access to certain prefixes, etc. This avoids overly complex single bucket policies.
  * **Data Transfer and Bandwidth:** When moving large volumes of data to/from S3, consider network implications. Within AWS, transfers to S3 are generally free (and egress from S3 to EC2 in the same region is free), but inter-region or internet egress is chargeable. Use **Amazon CloudFront** for content delivery to end-users to leverage edge caching and reduce direct S3 downloads over the internet. For long-distance high-latency uploads (e.g., from remote clients to a central S3 bucket), enable **S3 Transfer Acceleration**, which uses AWS edge locations to accelerate data transfer into S3 over AWS’s network backbone. For extremely large datasets (terabytes or petabytes) that are impractical to transfer over the network, AWS Snow Family devices (Snowball, Snowmobile) can be used to physically transfer data to S3.
  * **Hybrid Connectivity:** If integration between on-premises and S3 is needed (e.g., a corporate data center application pushing data to S3), leverage AWS Direct Connect with the S3 Endpoint (this avoids public internet) or use VPN into a VPC with a Gateway Endpoint. AWS Storage Gateway can also present a file interface (NFS/SMB) that backs up into S3, useful for legacy systems expecting file shares.

* **Networking & Security Architecture:** *TBD* (To be defined – likely a reference architecture diagram or description for how S3 fits into PepsiCo’s network and security landscape, e.g., showing VPC endpoints, flow through Direct Connect, etc.)

* **AWS Service Limitations:**

  * **Bucket Quotas:** Each AWS account can create up to **1000** S3 buckets by default (this was recently raised from 100; currently 1000 general-purpose buckets is the default as of Nov 2024). In practice, AWS now allows up to **10,000 buckets** by default per account and you can request an increase to even higher (up to 1 million) if needed. While there is technically no limit to the number of objects in a bucket (you can have billions of objects), having extremely large buckets may impact management tasks (like listing objects might become slow or costly). It’s often wise to partition data into multiple buckets for different applications or retention requirements rather than one huge bucket for everything.
  * **Object and Request Limits:** Maximum object size is **5 TB**. There is no limit on objects per bucket, and S3 can handle very high request rates. However, note the per-prefix performance guidelines: \~3,500 writes and 5,500 reads per second per prefix as a baseline. If you anticipate a sustained rate significantly above that, ensure your object keys are designed to distribute load (S3 will automatically partition as needed, but a single object cannot exceed these rates on its own). Also, the S3 **List API** (LIST bucket) will at most return 1000 keys at a time – listing extremely large buckets can be slow and requires pagination. The List API is eventually consistent if you don’t have strong read-after-write (but now it is strongly consistent, which means after an object is added or removed, a subsequent LIST will reflect it).
  * **Consistency and Replication:** S3’s strong consistency means that most traditional concerns about read-after-write inconsistency are gone (as of 2020, all operations including updates to object metadata are strongly consistent across a bucket). But one limitation is that **cross-region replication has some latency** and is not strongly consistent across regions – if you write and then immediately read from a replica region, you might not see the object until replication completes. Also, **replication is one-way** by default (unless you set up two-way replication with two rules). There’s no automatic failback; if you switch to a replica region, you’d need to set up replication in reverse or copy data back manually.
  * **Feature Limitations:** Certain S3 features have their own limits. **S3 Object Lock** (WORM) must be enabled at bucket creation time (you cannot enable Object Lock on an existing bucket unless it was created with that flag). **Multipart Uploads:** there’s a limit of 10,000 parts per multipart upload (and each part can be up to 5 GB, except the last one can be 5 TB minus 1). This effectively is how the 5 TB limit is enforced. If a multipart upload isn’t completed or aborted, the parts already uploaded will persist and incur storage charges – it’s recommended to use lifecycle rules to abort multi-part uploads that don’t complete within a certain time to avoid "stranded" storage. **Object Metadata:** Each object can have up to 2 KB of metadata (HTTP headers), and keys (names) can be up to 1024 bytes. **Object Tagging:** maximum 10 tags per object. **Access Points:** up to 1,000 per bucket. These are seldom limiting but should be known if designing at scale.
  * **KMS Encryption Throttling:** When using SSE-KMS encryption, keep in mind the KMS throughput. By default, AWS KMS may throttle if there are more than around 5,000-10,000 requests per second to a single CMK (Customer Managed Key) – which can happen if you are PUTting thousands of objects per second encrypted with the same KMS key. If your use case approaches this scale, you can request a quota increase for KMS, use multiple keys (shard your data across different CMKs), or use the **KMS encryption context caching** provided by the AWS SDK to reduce load. Monitor the CloudWatch metric for KMS request throttling.
  * **No Native File System Mount:** S3 is not a POSIX file system, so it cannot be mounted like a disk without using third-party tools (e.g., s3fs or File Gateway) and doesn’t support file locking or POSIX semantics. Any application expecting a normal file system (with atomic rename, partial writes, etc.) will require an intermediate layer or a different storage service (like EFS or FSx). S3 also has eventual consistency for certain operations like LIST in replication scenarios, as discussed, and no built-in notion of transactions or record-level locks. These characteristics mean S3 is immensely scalable but not suitable for scenarios requiring real-time concurrent writes to the *same* object from multiple clients (the last writer wins with no merging or locking).

* **SKU Features:**

  * **S3 Standard:** The default storage class for general-purpose use. Provides **high durability (11×9’s)** and **stores data across at least 3 AZs**. S3 Standard is optimized for **frequently accessed** data, offering low latency (first byte typically in tens of milliseconds) and high throughput. Designed for **99.99% availability** (with a 99.9% availability SLA). Use cases: content storage and distribution, web applications, data analytics that require frequent reads/writes. There are no retrieval fees or minimum retention period for Standard.
  * **S3 Intelligent-Tiering:** A smart storage class that **automatically optimizes costs** for data with unknown or changing access patterns. Objects in Intelligent-Tiering move between tiers: frequent access, infrequent access, and optional archive tiers based on actual usage. Data that isn’t accessed for 30 days moves to a cheaper infrequent tier, and after 90 days of no access it can move to an archive tier (instant retrieval archive), all without impact on performance. If data is accessed again, it moves back to the frequent tier. **No retrieval fees** are charged in Intelligent-Tiering and there are no minimum storage duration penalties (other than a small monthly per-object monitoring fee). It has the same performance as Standard in the frequent and infrequent tiers, and is **designed for 99.9% availability** (99% SLA). Ideal as a default for datasets where access is unpredictable – you get savings when objects become cold, without managing lifecycle rules.
  * **S3 Express One Zone:** A **high-performance single-AZ** storage class. Data is stored in one Availability Zone (not replicated to others) to achieve **10× faster access speeds** and up to **80% lower request costs** compared to S3 Standard. This class supports extremely high request rates (each “Express” bucket can handle millions of requests per second by design) and consistent single-digit millisecond latencies for read/write. **Availability is 99.95%** (with 99.9% SLA) despite being single-AZ, thanks to redundant devices in that AZ, but durability is still very high within the AZ (multiple copies in different racks). There is a risk of data loss if that entire AZ is destroyed, so it’s recommended to **replicate Express One Zone data elsewhere** for durability. Use cases: machine learning training data, analytics scratch space, or other workloads where **lowest latency** in a specific AZ is needed and the data can be regenerated or is transient.
  * **S3 Standard-Infrequent Access (Standard-IA):** A lower-cost storage class for data that is accessed only occasionally but still requires rapid retrieval when needed. Like Standard, it stores data across 3 AZs and offers the same low latency and throughput\*\*\*\*, but with storage priced much lower and a charge per GB when data is retrieved. Suitable for backups, older data, or DR copies that are rarely read. **Availability is 99.9%** (99% SLA). There is a **30-day minimum charge** for data stored (if deleted sooner, you pay for 30 days) and a 128 KB minimum object size (smaller objects are charged as if 128 KB). When designing, ensure that infrequent access data won’t be accessed so often that retrieval fees outweigh storage savings.
  * **S3 One Zone-Infrequent Access (One Zone-IA):** Similar use case as Standard-IA but costs \~20% less, by keeping data in only **one AZ** instead of three. One Zone-IA still maintains 11×9’s durability within that AZ via redundant copies on multiple devices, but an AZ outage or loss could result in permanent data loss. It’s **ideal for secondary backups or re-creatable data** that you can tolerate losing (or that is already stored elsewhere). For example, if you have on-premises backups and just want a cheap additional copy in the cloud, One Zone-IA could be suitable. **Availability is 99.5%** (99% SLA). It has the same 30-day minimum storage and per-GB retrieval fee as Standard-IA. Generally, avoid One Zone-IA for any data that you cannot afford to lose; consider it only if either the data is non-critical or you have it replicated to another region.
  * **S3 Glacier Instant Retrieval:** An **archive** storage class that offers the lowest storage cost for data that is rarely accessed *but* needs immediate retrieval when requested. It stores data in 3 AZs and provides **milliseconds retrieval latency** like the standard classes. Storage cost is extremely low (cheaper than Standard-IA by up to 68% for data accessed once per quarter), but you pay a retrieval fee per GB when you do access data. **Availability is 99.9%** (99% SLA) and minimum storage duration is 90 days. This class is great for archives that might occasionally be needed on-demand (e.g., medical images, news footage archives, or user data archives that users may request infrequently). If data in this class becomes frequently accessed, it might incur high retrieval costs, so for changing access patterns it might be better to use Intelligent-Tiering (which can automatically promote it out of archive tier).
  * **S3 Glacier Flexible Retrieval (formerly Glacier):** A very low-cost archive class for data that **does not require instant access**. Data is stored across multiple AZs for durability. Retrieval options include expedited (minutes), standard (3-5 hours), and bulk (5-12 hours, free for bulk). It’s ideal for backups or archives where an occasional restore can be waited on for a few hours. **Availability is 99.99%** (99.9% SLA). Minimum storage duration is 90 days. This class is cheaper than Glacier Instant for storage, but the trade-off is the longer retrieval times. There is also a provision for free bulk retrievals which can be useful for large-scale DR restores. Use case: quarterly full backups stored in Glacier Flexible – in a disaster, you initiate a bulk retrieval to restore everything in \~12 hours, which is acceptable for many DR plans.
  * **S3 Glacier Deep Archive:** The **lowest-cost storage** in S3, meant for *true* cold storage and digital preservation. Deep Archive is priced very cheaply for storage and is intended for data that may be accessed maybe once a year or once in many years. Retrieval time is **12 to 48 hours**. It’s designed for use cases like compliance archives, long-term scientific data retention, or tape replacement. **Availability is 99.99%** (99.9% SLA) similar to Glacier Flexible. There is a **180-day minimum retention** charge on any data in Deep Archive. When you need to retrieve, you initiate a restore job and wait \~12 hours for bulk (up to 48 hours for very large or standard retrieval). Because of the extended time, this class should only be used for data that clearly will not be needed urgently. All restores from Deep Archive are charged per GB and per request. In an enterprise context, Deep Archive might be used for things like financial records that must be kept for 7-10 years for regulatory reasons but are almost never accessed.
  * **S3 on Outposts:** S3 on Outposts allows deploying S3 API-compatible object storage to an AWS Outposts rack on-premises. This means you can create S3 buckets that physically reside on your Outposts (in your data center) for **data residency or low-latency local storage needs**. S3 on Outposts buckets are separate from AWS region buckets; data stored does not leave the Outpost (except for backup/replication if configured). It supports a subset of S3 features – for example, currently versioning, Object Lock, and replication between Outposts or to region might have limitations. You must use SSE-KMS encryption with Outposts (and the KMS key must be local to the Outposts or synced). The number of buckets on an Outposts and total storage is limited by the Outposts capacity. Use cases: factories or locations that need to collect and store data locally due to bandwidth or compliance, but still want S3-like interfaces; or fast local processing of data with periodic syncing to the cloud. Outposts S3 still integrates with AWS IAM for access control and uses the same S3 APIs, but you will access it via a different endpoint specific to your Outpost. **Note:** Data on Outposts is only as durable as the Outpost itself (typically redundancies within the Outpost, but not across multiple sites unless you replicate manually). Ensure that any critical data on Outposts is copied to the cloud or another Outpost for backup.

* **Related Service:** *TBD* (e.g., AWS EFS or EBS for other storage types, Azure Blob Storage as alternative, etc., to be defined by enterprise architecture)

## Compliance and Security Guidelines

* **Security Baseline InfoSec:** *Information Security Specifications – Amazon S3* (Refer to PepsiCo internal InfoSec baseline document for AWS S3, which outlines mandatory security controls such as encryption, access approvals, monitoring, data classification handling, etc., when using S3.)

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * v.1: 22 Jul 2025 (Dariusz Korzun)

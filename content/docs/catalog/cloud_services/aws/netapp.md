---
weight: 7
title: "NetApp"
date: 2025-07-22
tags: []
summary: ""
---

# Amazon FSx for NetApp ONTAP

## Cloud Service Classification
- **Category of the Service:** Storage  
- **Cloud Provider:** AWS  

## Cloud Service Overview
- **Name:** Amazon FSx for NetApp ONTAP  
- **Description:**  
  Amazon FSx for NetApp ONTAP is a fully managed AWS storage service that provides highly reliable, scalable, high-performance shared file storage built on NetApp’s ONTAP file system. It combines NetApp’s enterprise data management features (such as snapshots, clones, and replication) and multi-protocol support (NFS, SMB, iSCSI, NVMe-oF) with the agility and simplicity of a managed AWS service. This service enables customers to access file storage from Linux, Windows, and macOS environments without changing application code, while benefiting from ONTAP’s advanced capabilities (e.g. compression, deduplication, automatic tiering to low-cost storage) in the cloud.
- **SKU Approval Status:**  
  - **PepsiCo Approved:**  
    - **Multi-AZ Deployment (HA):** Fully supported for production use. Multi-AZ file systems replicate data across two Availability Zones and provide automatic failover for high availability.  
    - **Single-AZ Deployment:** Allowed only for non-production workloads (e.g. development, testing, or secondary data copies). Single-AZ file systems offer lower cost and higher performance (no cross-AZ overhead) but lack AZ-level resiliency.  
  - **Not Approved:**  
    - No other deployment options. (All current FSx for ONTAP modes are covered above.)  
- **Allowed PepsiCo Data Classification:**  
  - Public  
  - Internal  
  - Confidential  
  - Restricted  

## Service Lifecycle
- **Release Date:** September 2, 2021 (General availability launch).  
- **Planned Decommission Date:** No announced decommissioning plans.  
- **Decommission Date:** Not applicable at this time.  

## Usage Guidelines
- **Features:**  
  - **Multi-Protocol File Access:** Supports industry-standard file access protocols including NFS (v3, v4.x), SMB (all versions), iSCSI, and NVMe-over-TCP. FSx for ONTAP allows concurrent NFS and SMB access to the same data, enabling seamless file sharing between Linux/UNIX and Windows clients. It also supports shared block storage via iSCSI/NVMe, covering a broad range of application needs.  
  - **High Performance and Scalability:** Delivers petabyte-scale storage in a single namespace and high throughput – up to tens of gigabytes per second per file system – with consistent sub-millisecond latency on SSD storage. Each file system’s performance can scale to hundreds of thousands of IOPS and is tunable via provisioned throughput capacity. FSx for ONTAP can serve thousands of concurrent client connections (from EC2, EKS/ECS, VMware Cloud on AWS, etc.) without performance degradation.  
  - **Advanced Data Management (Snapshots, Clones, Replication):** Includes NetApp ONTAP’s rich data management capabilities. You can take point-in-time Snapshot copies of volumes for quick backups or user file restore (viewable via “Previous Versions” in Windows). The FlexClone feature enables instantaneous, writable clones of volumes that initially consume no additional storage (copy-on-write), useful for testing or analytics on production data. FSx for ONTAP fully supports NetApp SnapMirror replication, allowing efficient block-level data replication between on-premises ONTAP systems and AWS or between FSx file systems for backup and disaster recovery.  
  - **Storage Efficiency and Tiering:** Optimizes cost by automatically tiering infrequently-accessed data to a capacity pool tier backed by lower-cost object storage. Data that becomes “cold” is moved to the elastic capacity tier, which can scale virtually without limit, while hot data stays on high-performance SSDs. FSx for ONTAP also applies inline compression, deduplication, and compaction to all data, significantly reducing the storage footprint and costs. These efficiencies let you achieve SSD performance for active data while paying for expensive storage only for the portions of data that are hot.  
  - **AWS Integration and Management:** Deeply integrated with AWS management and security services. You can manage file systems through the AWS Management Console, CLI, SDKs, or via NetApp’s native tools like ONTAP CLI and BlueXP Cloud Manager. The service integrates with AWS IAM for controlling administrative operations, AWS CloudTrail for audit logging of API calls, and Amazon CloudWatch for monitoring performance and usage metrics. FSx for ONTAP is also integrated with AWS Key Management Service (KMS) for at-rest encryption keys and can join Microsoft Active Directory for identity management of SMB access. Security features include encryption in transit (support for Kerberos SMB encryption, IPsec for NFS, and AES-256 encryption via Nitro hardware) and support for ONTAP’s FPolicy and “vscan” interfaces to plug in third-party security or antivirus solutions.  

- **Sample Use Cases:**  
  1. **Migrate On-Premises NAS Data:** Lift-and-shift existing file-based workloads to AWS without application changes. FSx for ONTAP allows enterprises running NetApp ONTAP or other NAS (NFS/SMB/iSCSI) on-premises to migrate data to the cloud and retain the same features and management processes. For example, an organization can replicate on-prem NetApp volumes to FSx for ONTAP via SnapMirror and cut over applications to AWS, preserving file paths and access protocols.  
  2. **High-Performance File Storage for Databases & Analytics:** Provide low-latency, high-throughput shared storage for performance-intensive applications such as databases (Oracle, SAP HANA, Microsoft SQL Server) and big data/analytics or EDA workloads. FSx for ONTAP’s SSD storage and scale-out architecture deliver the IOPS and bandwidth needed for these workloads, while features like quality of service (QoS) controls and multi-volume layout help ensure consistent performance.  
  3. **Dev/Test Environments with Instant Clones:** Accelerate development and testing by using FlexClone to create instant, space-efficient copies of large datasets. For instance, a QA team can take a snapshot of a production database and instantly clone it to a test environment, perform experiments or upgrades on the clone, then delete it when done – all without doubling storage usage or affecting production. This speeds up application release cycles by enabling realistic testing on production-sized data.  
  4. **Multi-Platform File Sharing:** Enable concurrent access to the same data from both Windows and Linux environments. FSx for ONTAP’s multi-protocol support is ideal for applications that require cross-platform compatibility – for example, a data science platform where data is ingested via Windows-based tools over SMB and later processed on Linux-based analytics servers over NFS. Both sets of clients can securely access the one shared dataset without duplication.  
  5. **Disaster Recovery and Backup:** Implement robust business continuity for file data. Using SnapMirror replication, you can maintain an up-to-date secondary copy of your data on another FSx for ONTAP file system in a different AWS region (or on-premises) with an RPO as low as 5 minutes. In a disaster scenario, applications can be failed over to the secondary file system within minutes (RTO in single-digit minutes). Additionally, the service’s built-in backup capability (daily automated backups and on-demand backups) lets you archive point-in-time volume backups to Amazon S3, which can be restored to a new file system for recovery or auditing. This multi-layered approach (snapshots, cross-region replication, and backups) helps protect against both infrastructure outages and data corruption or ransomware attacks.  

- **Limitations:**  
  - **Single-AZ vs Multi-AZ Resiliency:** Single-AZ deployments are not resilient to Availability Zone failure – if the AZ hosting the file system goes down, the file system becomes unavailable until that AZ recovers. (Single-AZ FSx for ONTAP keeps data replicated only within the AZ.) Therefore, Single-AZ is recommended only for non-critical use cases or where data is replicated elsewhere. Multi-AZ deployments, by contrast, keep two copies across AZs and can fail over during AZ outages, so they should be used for high-availability needs.  
  - **Provisioned Throughput Ceiling:** Each FSx for ONTAP file system has a fixed throughput capacity that you choose (and can adjust). If your workload attempts to exceed the provisioned throughput or IOPS, it will encounter throttling or elevated latencies. While you can increase throughput capacity (even dynamically), doing so triggers a brief failover event during which I/O might pause for a few seconds before resuming on the new configuration. Careful capacity planning and monitoring are needed for very I/O-intensive workloads to avoid hitting this limit.  
  - **Storage Quota Constraints:** There are hard limits on certain resources. For example, each FSx for ONTAP file system can contain up to 500 volumes (or up to 1,000 volumes in multi-node Single-AZ clusters). Each volume can have a maximum of 1,023 snapshot copies. If more than 500–1,000 distinct datasets or isolation units are needed, multiple file systems or a different architecture may be required. These quotas can be increased via AWS Support for special cases, but they define the baseline limits.  
  - **No Native Multi-Region Sync:** The service does not offer built-in automatic multi-region replication or failover (unlike some global services). Cross-region disaster recovery must be implemented using SnapMirror (for near-real-time replication) or by copying backups to another region. This means failover across regions is not instantaneous and must be orchestrated by the user (with an RPO/RTO in minutes). There is also no single global namespace – file systems are region-specific, so apps in different regions need separate instances or a caching strategy.  
  - **Third-Party Antivirus/Security Integration:** While FSx for ONTAP supports on-demand virus scanning and file screening via NetApp’s FPolicy/vscan features, AWS does not provide a built-in scanning service. Customers must deploy and manage third-party antivirus software (on EC2 instances or on-prem) to leverage this feature. This adds complexity for organizations needing malware scanning or DLP on their file data, as they must maintain additional infrastructure for full security coverage.  
  - **Learning Curve and Management Overhead:** Using FSx for ONTAP may introduce the complexity of NetApp ONTAP administration. Concepts like Storage VMs (SVMs, a.k.a. virtual filers), aggregates, volume tiering policies, and ONTAP CLI/GUI management are part of the service. Teams unfamiliar with NetApp will face a learning curve to fully exploit advanced features. Additionally, configuration choices are fixed at creation for some aspects (e.g., Single-AZ vs Multi-AZ, throughput capacity baseline) and require careful planning – switching an existing deployment from Single-AZ to Multi-AZ, for instance, is not possible without migrating to a new file system.  
- **Additional Guardrails:** TBD  
- **Used By:** TBD (list & links to some onboardings)  
- **EA Declaration:** NOT a declared standard. (This service is not yet an officially declared standard in Enterprise Architecture.)  

## Technical Guidelines
- **Best Practices:**  
  - **Deployment Configuration:** Choose the deployment type based on resiliency vs. performance needs. Use Multi-AZ file systems for production workloads that require high availability and durability across AZs – Multi-AZ FSx for ONTAP keeps an active-passive pair of file servers in two AZs with synchronous replication and automatic failover within ~60 seconds. Use Single-AZ file systems only for use cases that can tolerate AZ outages (e.g. dev/test, or where data is replicated elsewhere). Single-AZ can be more cost-effective and offer slightly lower latency (no cross-AZ network hop) and can also support scale-out clusters with multiple HA pairs for higher performance, but it sacrifices AZ-level resiliency. Note: You cannot convert a Single-AZ deployment into Multi-AZ (or vice versa) after creation; switching requires deploying a new file system and migrating data. Plan the choice of Single vs. Multi AZ upfront according to your availability requirements.  
  - **Data Tiering Policies:** Leverage ONTAP’s volume tiering policies to balance performance and cost. For critical high-IO datasets (e.g., HPC or database volumes), set the tiering policy to “None” or “Snapshot Only” so that active data remains on SSD and isn’t tiered to S3 (avoiding added latency). For datasets that are mostly cold or archival, use the “Auto” policy (which tiers infrequently used blocks after a cooling period) or “All” (tiers everything immediately) to minimize SSD storage usage. For example, during initial data migrations, setting volumes to “All” can push all data to the capacity tier and prevent the initial load from filling expensive SSD capacity. Monitor how much data sits in each tier and adjust policies per volume to achieve an optimal cost/performance mix.  
  - **Monitoring and Alerts:** Utilize Amazon CloudWatch metrics and alarms to keep tabs on file system health and capacity. FSx for ONTAP publishes key metrics at both the file system and volume level (e.g., throughput, IOPS, latency, storage used). It’s recommended to track StorageCapacity and StorageUsed metrics by tier – for instance, set a CloudWatch alarm if SSD tier usage exceeds ~80% of provisioned capacity. This alerts you to expand SSD or adjust tiering before performance is impacted. Also monitor ClientConnections and Throughput utilization to ensure you’re within the limits of your chosen throughput capacity. Proactively right-size or scale your file system (via increasing throughput or adding an HA pair in Single-AZ) if sustained usage approaches limits.  
  - **Backup and Recovery Strategy:** Use a combination of ONTAP snapshots, FSx backups, and cross-region copies to meet your recovery objectives. Snapshots (which are point-in-time volume copies on the file system) can be scheduled frequently (e.g., hourly) to allow quick rollbacks of recent changes or protection against accidental deletions; they are fast and space-efficient but reside in the same file system (so not immune to catastrophic failure). For durable backups, integrate AWS Backup to take regular FSx for ONTAP volume backups (these are stored independently of the file system, in S3). Automated daily backups are enabled by default on FSx – verify the retention period (default is 7 days, max 90 days) and adjust to comply with your data retention policies. If needed, set up AWS Backup to copy backups to another region or account for added resiliency. Periodically test restoration procedures by simulating volume restores from backups and promoting SnapMirror destinations to ensure your team is prepared for real disasters.  
  - **Data Organization and Volume Design:** Design your volumes and Storage Virtual Machines (SVMs) thoughtfully. Group data with similar access patterns and security requirements into separate volumes – this allows applying appropriate tiering policies and snapshot schedules per volume, and limits the blast radius of any issues. Use multiple SVMs if needed to segregate environments or departments (each SVM can join a different Active Directory domain or have distinct admins). Keep directory sizes within reasonable bounds; note that ONTAP has a `maxdirsize` setting controlling how many files can reside in a single directory (default ~4.3 million files). Extremely large directories can degrade performance, so if an application will store tens of millions of files in one directory, consider sharding into subdirectories or increasing `maxdirsize` (with caution, as it uses more memory). Also, follow NetApp best practices for volume and aggregate layout if doing heavy parallel writes (e.g., consider multiple volumes or FlexGroup volumes for high ingest workloads).  
  - **Security and Access Management:** Integrate FSx for ONTAP with your existing identity and security controls. If using SMB, join the file system to your Active Directory so that you can manage share permissions and file ACLs through domain users/groups – this ensures consistent authentication with on-prem AD if in a hybrid setup. Use the least-privilege principle: for example, avoid using the FSx built-in “admin” account for routine operations; instead, create custom ONTAP SVM administrators with limited rights or use AWS IAM to control who can perform management actions via AWS APIs. Disable unused protocols – if you only need NFS, you can avoid enabling SMB on a volume (and vice versa) to reduce attack surface. Regularly export and review audit logs: FSx for ONTAP can log file access events (ONTAP FPolicy audit) to a central location for analysis, and CloudTrail logs all API calls, which should be monitored for unexpected activities. Encrypt data in transit wherever applicable: for SMB clients, enforce SMB encryption or signing (especially when traversing untrusted networks); for NFS clients over VPN/Direct Connect, rely on IPsec tunnels or Kerberos NFS authentication to prevent sniffing.  

- **High Availability & Disaster Recovery:**  
  - **In-Region High Availability:** FSx for ONTAP offers robust in-region HA, especially with Multi-AZ deployments. A Multi-AZ file system consists of two file server nodes in different AZs, configured in an active-standby pair with synchronous replication of data. In the event of a node failure or an AZ outage, FSx automatically fails over to the standby node, typically within 60 seconds, and clients reconnect to the alternate endpoint automatically. This provides continuous availability to data even if an entire AZ goes down. Single-AZ file systems, while lacking AZ redundancy, still have high availability within the AZ – they are deployed as one or more HA pairs such that if one controller fails, its partner takes over. In both cases, FSx continuously monitors hardware and will replace failed components or perform self-healing as needed. Design consideration: if using Multi-AZ, ensure your client instances are configured (via mount targets or DNS) to use the file system’s DNS name, which resolves to the active endpoint and will update on failover. Also test the failover process to understand any brief pauses or effects on your specific application (most NFS/SMB clients will handle the reconnect seamlessly).  
  - **Cross-Region DR:** For disaster recovery across regions, use NetApp SnapMirror replication or cross-region backups. SnapMirror allows you to configure scheduled asynchronous replication from an FSx for ONTAP volume to a volume on another FSx for ONTAP file system in a different region. You can achieve an RPO as low as 5 minutes with SnapMirror, thereby limiting data loss in a regional failure. In a DR scenario, you would break the SnapMirror relationship and “fail over” by mounting the destination volume (now read-write) in the DR region, pointing applications to it. The expected Recovery Time Objective is on the order of minutes (to update DNS/clients) since the data is already in place. Alternatively, for simpler DR (with longer RTO/RPO), you can rely on AWS Backup with cross-region backup copies. For example, schedule nightly backups of your FSx volumes and have AWS Backup copy them to a vault in another region. This provides an off-line, secure copy of data; the RPO is the backup interval, and RTO involves restoring the backup to a new file system (which could take hours for multi-terabyte datasets). Decide the strategy based on business requirements: SnapMirror for near-real-time hot-standby (requires running a second FSx file system continuously), vs. backups for cost-effective insurance. It’s also possible to combine both – e.g., SnapMirror to a secondary FSx for quick failover, plus periodic backup copies for compliance and ransomware-protected backups.  
  - **Failover Testing and Automation:** Regularly test failover and recovery procedures. In Multi-AZ setups, you can simulate an AZ outage (AWS allows inducing a failover via the console or CLI for FSx for ONTAP) to verify that your monitoring catches the event and that clients reconnect properly. Document the process for failing over and failing back in a SnapMirror DR scenario – including DNS updates, application restart steps, etc. – and consider automating it with scripts or AWS Systems Manager for quicker response. Ensure that any dependencies like Active Directory (for SMB) or client mount configurations are also redundant across regions/AZs (e.g., have a secondary domain controller in the DR region, use DNS CNAMEs for file shares that can be redirected).  

- **Backup & Recovery:**  
  - **Automated Daily Backups:** By default, Amazon FSx for NetApp ONTAP performs a daily automatic backup of each file system, retaining the backups for a user-specified period (7 days by default, up to 90 days maximum). Ensure this is configured according to your data retention needs – for critical data, you might increase the retention or take multiple backups per day if the change rate is high (though SnapMirror might be preferred for shorter intervals). Automated backups are taken during a daily backup window and are crash-consistent (they capture all volumes’ state at the time of backup). Keep in mind these backups reside in the same AWS region by default; if regional durability is a concern, use AWS Backup to copy them out.  
  - **User-Initiated Backups:** You can take manual backups at any time (via console, CLI, or AWS Backup API) – for instance, before a major application upgrade. These FSx backups are stored as incremental snapshots in S3 (only changed blocks since the last backup are saved), which minimizes storage costs. Each backup is immutable and can be used to restore the state of a volume or file system. Restoring from backup can be done to a new file system or new volume (the service allows volume-level restores onto the same or another FSx ONTAP). It’s a best practice to tag your critical volumes and set up an AWS Backup plan to capture them on a schedule, ensuring nothing is missed. Also, verify that your backup vault has access policies and encryption as required by your compliance (AWS Backup integrates with KMS for backup encryption as well).  
  - **Snapshot Management:** Leverage ONTAP Snapshot copies for fast on-box recovery. Snapshots can be taken very frequently (even hourly or more) with negligible performance impact since they are just pointers to data blocks. They are useful for operational recovery (e.g., a user accidentally deletes or overwrites a file – it can be restored from a snapshot in seconds). Implement a snapshot schedule for each volume that matches the data criticality – e.g., hourly snapshots retained for 24 hours, daily snapshots retained for a week, weekly for a month, etc. Keep an eye on used snapshot space; although snapshots are efficient, if data is overwritten heavily, snapshots will keep old blocks and consume space. You might use ONTAP’s snapshot autodelete policies to free space if volume space gets tight (or simply monitor and manually delete older snapshots as needed). **Note:** Snapshots alone do not protect against storage system loss since they reside on the same FSx file system – they complement backups but are not a substitute for them.  
  - **Restore Testing:** Periodically test your restore processes. For Snapshots, test file restores (for SMB, users can use the Windows “Previous Versions” UI; for NFS, you can browse the “.snapshot” directory) to ensure users know how to self-serve or admins can quickly recover data. For FSx Backups, perform a trial restore to a new volume or clone file system – measure how long it takes and whether the restored data is consistent. This will give confidence that in an actual incident, you can meet your RTO. For SnapMirror DR, test a failover by breaking replication to a test copy and bringing it online read-write (and later re-establishing relationships). After testing, incorporate any improvements (maybe you found you need a bigger FSx in DR region or to quiesce applications before backup to ensure consistency, etc.).  

- **Access Control & Security Configuration:**  
  - **Authentication & Authorization:** Integrate with Active Directory for identity management. When using SMB, FSx for ONTAP requires joining an AD domain (either AWS Managed Microsoft AD or self-managed) – this allows domain users to authenticate and access file shares using their existing credentials. Use AD group membership to control access via share permissions and NTFS ACLs on files/folders. For NFS, you can use UNIX identity (UID/GID) or configure LDAP/NIS if available. FSx ONTAP supports NTFS-style ACLs even on NFS exports (in multi-protocol volumes), which can unify access control across SMB/NFS. Leverage ONTAP’s export policy rules to restrict NFS client access by source IP and enforce read/write permissions as appropriate.  
  - **IAM Control for Management:** Use AWS IAM to tightly control who in your organization can perform FSx management actions (creation, deletion, backup, etc.). For example, grant the `FSx:CreateFileSystem` and `FSx:DeleteFileSystem` permissions only to infrastructure administrators, and use AWS Backup IAM policies to allow backup operators to create/restore backups but not delete file systems. All operations should be audited via CloudTrail – set up CloudTrail logs and possibly Amazon EventBridge rules to alert on sensitive changes (like a file system deletion event or a policy change).  
  - **Encryption:** FSx for ONTAP automatically encrypts all data at rest using AWS Key Management Service. By default, an AWS-managed KMS key is used, but you have the option to specify a customer-managed KMS key for the file system’s encryption if you need to manage key rotation or meet specific compliance controls. Ensure you track and rotate the customer-managed key per your security policy. All backups taken from FSx are also encrypted at rest. For **data in transit**, enable encryption whenever possible: SMB protocol can enforce encryption (and signing) when clients and the server negotiate it (this can be set via Group Policy on Windows clients or by requiring SMB3 encryption on the share). NFS traffic can be encrypted by running it through a VPN or using Kerberized NFS (ONTAP supports NFSv4 with Kerberos authentication which provides integrity and privacy). Additionally, AWS Nitro hardware provides automatic encryption of traffic between EC2 instances and FSx within the same AWS region (for supported instance types), so traffic on the AWS backbone is protected – however, this doesn’t cover end-to-end if you have on-prem clients. Always consider the network path and apply TLS/IPsec where needed for any data traveling over unsecured networks.  
  - **Network Security:** Deploy FSx for ONTAP in private subnets and use Security Groups and ACLs to limit access. When you create an FSx file system, you specify the VPC and subnets; ensure these subnets are not directly exposed to the internet. FSx attaches Elastic Network Interfaces (ENIs) to your VPC – you should configure the FSx security group to allow NFS/SMB/iSCSI traffic only from known client subnets or instances. For example, allow TCP port 2049 for NFS from your application servers’ security group, and TCP 445 for SMB from your Windows server subnets, and block all other sources. If using on-prem access via Direct Connect/VPN, limit the inbound connectivity to the FSx endpoints to only your corporate IP ranges. It’s good practice to also enable in-transit encryption for SMB shares at the share level and require signed/encrypted LDAP for AD communications to avoid any sniffing of credentials. Lastly, consider implementing a dual-admin model for security: AWS IAM admins manage the FSx provisioning, while NetApp ONTAP admins (with ONTAP CLI or BlueXP) manage the file-level configuration – make sure both sets of credentials are protected and regularly reviewed.  
  - **Auditing and Compliance:** FSx for ONTAP is compliant with various security standards (SOC, PCI, ISO, etc. per AWS documentation), but you must enable and use its features to meet internal compliance. Turn on ONTAP Audit Logging for file access if you need to track user access to sensitive files. These audit logs can be shipped to a central SIEM or Amazon CloudWatch Logs for retention and analysis. If using SnapLock (WORM storage) for regulatory compliance, configure SnapLock volumes in Compliance mode for data that must be write-once-read-many (e.g., certain logs or archives). Ensure your InfoSec team reviews the configuration against corporate policies – for instance, verifying that only approved ciphers are used for encryption in transit (ONTAP supports AES-256 for Kerberos and TLS). In summary, treat FSx ONTAP like an extension of your on-prem file storage – apply the same rigor of permission reviews, data classification labeling, and monitoring on this cloud service as you would in your own data center.  

- **Network Connectivity Options:**  
  - **Public Endpoint:** **NOT ALLOWED.** Amazon FSx for NetApp ONTAP does not provide a public internet endpoint for data access. File systems can only be accessed within your VPC (or via networks connected to your VPC). By design, the service is isolated to private IP addresses – there is no supported way to mount or reach an FSx for ONTAP file share directly from the public Internet. Any attempt to expose it (e.g., via a proxy or EC2 instance with a public IP) would violate PepsiCo security guidelines.  
  - **VPC Access (Private Subnet):** **Required.** FSx for ONTAP must reside in one or more subnets of an AWS VPC. Clients in the same VPC connect via the file system’s DNS name or IP (which resolves to a private IP in the VPC). For Multi-AZ deployments, the file system has endpoints in two subnets (one per AZ), and clients should use the DNS name to automatically connect to the active endpoint. Access from other AWS networks can be facilitated using VPC Peering or AWS Transit Gateway: for example, applications in a different VPC (even in a different account) can mount the FSx file system if the VPCs are peered and security rules allow it. All traffic remains on AWS’s private network.  
  - **On-Premises Access:** **Supported via VPN/Direct Connect.** You can access FSx for ONTAP from on-premises environments by establishing a secure network connection to the AWS VPC. Typically this is done with an AWS Direct Connect private VIF or a Site-to-Site VPN tunnel to your VPC. Once connectivity is in place, on-prem servers can mount NFS exports or map SMB shares from the FSx file system’s private IP addresses (just as if it were a NAS in a remote office). Ensure that DNS resolution is in place for the FSx DNS name from on-prem (this may involve forwarding queries to AWS DNS) or use the file system’s IPs directly. Latency between on-prem and AWS should be taken into account – for frequently accessed data by on-prem users, consider using NetApp FlexCache on-prem to cache hot data, or only allowing on-prem access for administrative or DR purposes.  
  - **Cross-Region Access:** **Not Directly Supported.** FSx for ONTAP does not natively allow mounting a file system from a different AWS region. If you need to access the same data in multiple regions, you should use SnapMirror replication to create copies of the data in each region or use an caching solution (e.g., an FSx File Cache or NetApp Global File Cache) to distribute data. Remote region clients would otherwise have to go over some WAN connection which is not advised for regular operation due to latency and potential data transfer costs. It is recommended to keep data access localized to the region where the FSx file system resides, and use replication for distributing data across regions for read-near or DR purposes.  
  - **Data Transfer and Bandwidth:** All client-server communication occurs within your network. For AWS internal traffic (say, EC2 in the same region accessing FSx), there are no data transfer fees for within-AZ access; however, note that if an EC2 in a different AZ (than a Single-AZ FSx) accesses it, standard cross-AZ data charges apply. In Multi-AZ FSx, clients always connect to the primary AZ endpoint (except during failover), so if you have clients in both AZs, the ones not co-located with the primary will incur cross-AZ charges. Architect your deployment to minimize unnecessary cross-AZ or cross-region data flows. Utilize AWS Direct Connect for on-prem access when high throughput or low latency is required; Direct Connect provides more consistent performance than internet VPN. Also monitor the network throughput – FSx throughput capacity defines the max bandwidth; if multiple clients will heavily use the file system concurrently, ensure the sum of their usage doesn’t saturate the FSx throughput or your VPC links.  
- **Networking & Security Architecture:** TBD  

- **AWS Service Limitations:**  
  - **Maximum Capacity and Scaling:** Each FSx for NetApp ONTAP file system has a finite amount of SSD storage you can provision (with second-generation file systems supporting up to 512 TiB of SSD per HA pair). Single-AZ file systems can be scaled out with multiple HA pairs, allowing up to 1 PiB of usable SSD storage in one file system cluster. However, if more storage is needed beyond these limits, you would need to deploy additional file systems (there is virtually no limit to total data thanks to the capacity tier, but the active working set on SSD is constrained by these per-file-system caps). Keep in mind that while capacity pool tier can grow without bound, extremely large datasets may require careful planning of retrieval performance (reading a petabyte of cold data from S3 tier will be limited by throughput and could incur costs).  
  - **Performance Scaling in Multi-AZ vs Single-AZ:** A Multi-AZ FSx for ONTAP is currently limited to a 2-node HA pair (one active, one standby), which means the maximum throughput for a single file system tops out at about 6,144 MB/s (≈ 6 GB/s) and up to 200,000 IOPS in total. In contrast, Single-AZ deployments (second-gen) support multiple HA pairs in a cluster (up to 12 pairs in theory), which can scale aggregate throughput much higher (dozens of GB/s) and millions of IOPS by distributing load across pairs. The trade-off is that Multi-AZ cannot horizontally scale beyond one pair. If an application requires more performance than a single HA pair can provide, it might need a Single-AZ multi-pair setup or to stripe data across multiple FSx file systems – which introduces complexity and reduces cross-AZ protection. This limitation should be considered when designing for very high throughput use cases: e.g., for 10+ GB/s requirements, you may decide to deploy Single-AZ clusters and accept AZ risk, or segment data into multiple Multi-AZ file systems.  
  - **Minimum Provisioned Resources:** FSx for ONTAP has a minimum footprint that might be overkill for very small workloads. You must allocate at least 1 TiB of SSD storage for an FSx ONTAP file system, and the smallest throughput capacity you can select is 128 MB/s (for first-gen) or 256–384 MB/s for newer deployments. This means even if you only need, say, 100 GB of data and minimal throughput, you will still be paying for a larger bundle of resources. The service is optimized for medium to large use cases; for very small-scale needs, other solutions (like EFS or FSx for Windows with smaller sizes) might be more cost-efficient. As a result, ensure that workloads deployed on FSx ONTAP justify its baseline provisioning – consolidate multiple small applications onto one file system if possible, to make use of the minimum 1 TiB/throughput.  
  - **Feature Gaps Relative to On-Prem ONTAP:** While FSx for ONTAP offers the majority of ONTAP features, a few niche features may not be available or have cloud-specific caveats. For example, FSx for ONTAP supports NetApp SnapLock (WORM) in Compliance and Enterprise modes, but it may not support every ONTAP version’s new features immediately upon release. Likewise, ONTAP’s multi-tenancy via SVMs is supported (up to 6 SVMs on first-gen, more on second-gen), but you cannot create infinite SVMs (there are limits per throughput tier). Also, some low-level ONTAP settings (like certain RAID group configurations, aggregate tweaks) are abstracted away by AWS – you don’t have the same level of hardware control as on-prem. These are generally not impactful to most users, but if you have very specialized ONTAP setups on-prem, confirm that FSx meets the requirements or be prepared to adjust the architecture.  
  - **Maintenance and Updates:** AWS manages software updates for FSx for ONTAP (applying patches, minor version upgrades of ONTAP). While this relieves operational burden, it means you have limited control on the timing of updates. AWS does schedule maintenance windows for FSx, during which an update may cause a failover to occur as part of patching. This typically is transparent and quick, but it’s a consideration in very latency-sensitive environments. Also, if you need a specific ONTAP version for application certification, note that AWS might not always offer the latest ONTAP version immediately – there could be a lag after NetApp releases a new version before AWS certifies it in FSx. Check AWS documentation for currently supported ONTAP versions and plan testing when major updates happen.  

- **SKU Features:**  
  - **Single-AZ FSx for ONTAP:**  
    - **Architecture:** One or more HA pairs within a single AZ (each HA pair is two controllers in an active/passive config sharing an AZ). Data is synchronously replicated between the two nodes of a pair for durability in case of node failure (but not AZ failure).  
    - **Scalability:** Supports scale-out clustering – you can add additional HA pairs to increase capacity and performance. A Single-AZ file system can have up to 12 HA pairs (depending on AWS’s current service limits), allowing the cluster to reach very high throughput (dozens of GB/s) and up to 1 PiB of primary SSD storage. Each additional HA pair adds compute and networking resources, enabling near-linear scaling for read/write workloads that can be spread across volumes on different pairs.  
    - **Performance Profile:** Offers the lowest latency since all client access is within one AZ (no cross-AZ network hops). Ideal for workloads like SAP HANA or other databases that benefit from single-digit millisecond latency and are certified on Single-AZ FSx ONTAP. Single-AZ also avoids the 2× write overhead of Multi-AZ (which must synchronously mirror to a second AZ), so write operations complete faster and consume less inter-AZ bandwidth.  
    - **Use Cases:** Development/test environments, analytics workloads requiring maximum throughput, or production apps that have alternative DR strategies. Also suitable when cost optimization is key – you’re not paying for a standby in another AZ. However, because an AZ outage will take the file system offline, it’s recommended to pair Single-AZ deployments with backups or SnapMirror to another region/AZ for resilience.  
  - **Multi-AZ FSx for ONTAP:**  
    - **Architecture:** An HA pair spread across two AZs. One node is active in the primary AZ, serving all client requests, while the other node in a secondary AZ is on standby. All writes are synchronously replicated from the primary to the secondary before being acknowledged, ensuring no data loss if the primary node or AZ fails. The file system presents endpoints in both AZs, but clients normally connect to the primary AZ’s endpoint (the secondary’s endpoint is only used during failover).  
    - **Scalability:** Supports only one HA pair per file system (current generation). There is no horizontal scaling with multiple pairs in Multi-AZ mode. Maximum SSD capacity is 512 TiB per Multi-AZ file system, and throughput tops out at ~6 GB/s (6,144 MB/s) given the single pair limits. If more performance or storage is needed than one pair can handle, you would need to deploy additional separate Multi-AZ file systems and perhaps divide your data (e.g., by application or dataset).  
    - **Performance Profile:** Slightly higher write latency due to cross-AZ mirroring (each write traverses to the standby AZ). Read latency is comparable to Single-AZ for data cached on the primary. Bandwidth is effectively the throughput of one node, and heavy write throughput will consume cross-AZ network bandwidth (potentially incurring costs, though AWS includes it in the service price for replication). Multi-AZ file systems guarantee redundancy – if the active node’s AZ has an outage, the standby takes over and clients reconnect typically within a minute, preserving service continuity.  
    - **Use Cases:** Production workloads requiring high availability and protection from AZ-level failures. For instance, departmental file shares or user home directories where any downtime would impact many users – Multi-AZ keeps these available through AZ disruptions. Also recommended for any workloads with Restricted data or stringent uptime SLAs, as it provides built-in fault tolerance. Keep in mind that if ultra-high performance is needed, Multi-AZ might be a limiting factor (since it can’t scale-out beyond one pair). In those cases, consider whether the workload can be split into multiple Multi-AZ file systems or if a compromise to Single-AZ plus external DR is acceptable.  
  - **Common Features:** Both Single-AZ and Multi-AZ support the full ONTAP feature set (snapshots, clones, SnapMirror, Active Directory integration, etc.) and have the same interface and APIs. They also both support the two-tier storage (SSD primary + S3 capacity pool) to optimize cost. Importantly, at-rest encryption, in-transit encryption options, and compliance certifications apply equally to both deployment types. From an operational perspective, AWS manages failover in both (Multi-AZ failover between AZs, Single-AZ failover between nodes in same AZ) – clients use the same DNS-based endpoint mechanism to reconnect. Pricing differs: Multi-AZ incurs costs for two AZ storage & replication, whereas Single-AZ is lower cost for storage but you may choose to run a second FSx in another region for DR which adds cost in a different way.  
- **Related Service:** TBD  

## Compliance and Security Guidelines
- **Security Baseline InfoSec:**  
  - Information Security Specifications – Amazon FSx for NetApp ONTAP (Refer to PepsiCo InfoSec baseline document for FSx ONTAP, which outlines required security configurations and controls compliance for this service – TBD by InfoSec.)

## Ownership and Version Control
- **Service Architect:** Dariusz Korzun – <EMAIL>  
- **Version Control:**  
  - v.1: 22 Jul 2025 – Document created (Dariusz Korzun)  
  *(Further revisions and approvals to be documented here.)*  
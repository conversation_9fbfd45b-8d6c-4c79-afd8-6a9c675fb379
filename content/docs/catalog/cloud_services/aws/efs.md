---
weight: 10
title: "EFS"
date: 2025-07-22
tags: []
summary: ""
---

# Amazon Elastic File System (EFS)

## Cloud Service Classification

* **Category of the Service:** Storage
* **Cloud Provider:** AWS

## Cloud Service Overview

* **Name:** Amazon Elastic File System (EFS)
* **Description:** Amazon EFS is a fully managed cloud file storage service that provides scalable, elastic, and shared file systems for use with AWS cloud services and on-premises resources. It offers a simple interface to create and configure file systems that automatically grow and shrink as files are added or removed, eliminating the need to provision storage in advance. EFS supports the NFSv4 protocol, providing standard file system semantics (strong consistency, file locking) accessible concurrently from potentially thousands of EC2 instances, containers, or Lambda functions. Data stored in EFS can be encrypted at rest and in transit, and the service is designed for high durability and availability, especially when using its multi-AZ storage classes.
* **SKU Approval Status:**

  * *PepsiCo Approved:* (TBD – to be updated by service owner upon evaluation of EFS storage classes)
  * *Not Approved:* (TBD)
* **Allowed PepsiCo Data Classification:**

  * Public
  * Internal
  * Confidential
  * Restricted

## Service Lifecycle

* **Release Date:** June 28, 2016 (general availability)
* **Planned Decommission Date:** No announced decommissioning plans.
* **Decommission Date:** Not applicable at this time.

## Usage Guidelines

* **Features:**

  * **Fully Managed Elastic File Storage:** Amazon EFS provides a serverless NFS file system that automatically scales on-demand from gigabytes to petabytes of data without provisioning capacity. There are no upfront fees; you pay only for the storage used. EFS supports standard file operations with strong consistency and file locking, making it easy to migrate existing applications that rely on a typical file system interface.
  * **Multi-AZ Durability and Availability:** In standard mode, EFS stores data redundantly across multiple Availability Zones in a region, offering high availability and **11×9s** durability (99.999999999% over a year) and the ability to sustain AZ-level failures. This makes it suitable for production workloads requiring resiliency. (For lower cost needs, EFS also offers One Zone storage classes that keep data in a single AZ with reduced durability and availability guarantees.)
  * **High Throughput and Parallel Access:** EFS is designed to support highly parallel workloads. It can serve thousands of concurrent NFS client connections, scaling aggregate throughput and IOPS as more clients and threads access the system. Throughput can scale to multiple gigabytes per second, and I/O operations are distributed, allowing applications like big data analytics, machine learning, and content serving to benefit from scaling out horizontally.
  * **Storage Classes and Lifecycle Management:** EFS offers multiple storage tiers for cost optimization. *Standard* storage is ideal for frequently accessed data, while *Infrequent Access (IA)* tiers offer lower cost storage for data not accessed often. These come in both **Standard-IA** (multi-AZ) and **One Zone-IA** (single AZ) variants. EFS’s **Lifecycle Management** (including EFS Intelligent-Tiering) can automatically move files to the IA tier after a specified period of inactivity, transparently reducing costs for cold data.
  * **Security and Compliance:** EFS supports encryption at rest using AWS Key Management Service (KMS) and encryption in transit (TLS) for all NFS traffic. Integration with AWS Identity and Access Management (IAM) allows for fine-grained access control on the file system’s API (and even NFS access, if enabled). EFS can be configured with **Access Points**, which are application-specific entry points with controlled access—useful for multi-tenant environments to enforce directory and permission isolation. The service is compliant with various security standards (SOC, PCI, HIPAA, etc., per AWS’s compliance programs), making it suitable for sensitive data when properly configured.
  * **Integration with AWS Ecosystem:** Amazon EFS is natively integrated with many AWS services. It can be mounted on **EC2** instances (including in Auto Scaling groups), used as persistent storage for **ECS** tasks or **EKS** pods via the EFS Container Storage Interface (CSI) driver, and accessed from **AWS Lambda** functions to persist state across invocations. EFS works with **AWS Backup** for automated backups, and with **AWS DataSync** for efficient data transfer and migration. On-premises servers can also access EFS over AWS Direct Connect or VPN, enabling hybrid cloud workflows.

* **Sample Use Cases:**

  * **Web Content Management & Shared File Store:** Host shared web content, static media, or user uploads on EFS to be accessed by a fleet of web or application servers across multiple AZs. For example, a pool of EC2 instances running a content management system or web application can read and write to the same set of files on EFS, ensuring consistency and simplifying deployment (no need for local instance storage syncing). This setup reduces bottlenecks on a single instance and provides high availability for file data.
  * **Enterprise Applications & Home Directories:** Migrate on-premises network file shares to AWS using EFS. Use cases include shared drives for business applications, user **home directories**, or departmental file shares that require concurrent access from multiple clients. EFS provides the POSIX-compliant file system interface these applications expect, simplifying cloud adoption for legacy enterprise apps (e.g., CRM, ERP systems needing a shared file repository). Fine-grained permissions and directory structures can mirror on-prem file systems, easing the transition.
  * **Big Data Analytics & Machine Learning:** Use EFS as a centralized data source for analytics jobs and machine learning training. Big data frameworks (Spark, Hadoop, etc.) or ML workloads can utilize EFS to access large datasets in parallel. For instance, a cluster of EC2 instances processing log data or training a model can simultaneously read from and write to EFS, leveraging its high throughput and parallel I/O capabilities. Unlike HDFS (which is tied to the cluster), data in EFS persists independently, and multiple clusters or services can access it as needed.
  * **Container Persistent Storage:** In containerized environments (Amazon ECS or Amazon EKS), use EFS to provide persistent volumes that can be shared across containers and services. This is useful for stateful microservices or workloads like content management, build systems, or shared caches in a Kubernetes cluster. EFS volumes can be mounted by multiple pods, enabling use cases such as distributed caching, shared configuration, or coordination through the file system. Because EFS is regional, it can be used by Kubernetes deployments across different AZs, and with Kubernetes **PersistentVolumeClaims**, pods can automatically mount the file system without direct user intervention.

* **Limitations:**

  * **Linux/NFS-Only Access:** Amazon EFS is accessible via the NFSv4 protocol and is **not supported on Windows** servers. Native Windows applications cannot mount EFS without third-party NFS clients (which are not officially supported by AWS). This means EFS is suited for Linux, Unix, and macOS environments. For Windows file-sharing needs (SMB protocol, Active Directory integration, etc.), AWS offers alternative services like Amazon FSx for Windows File Server.
  * **One Zone Trade-offs:** The **One Zone** storage classes (One Zone and One Zone-IA) store data in a single Availability Zone to reduce cost. These should *not* be used for critical data that requires high durability or availability. Data in a One Zone file system could be lost or become unavailable if that AZ suffers a failure. AWS recommends One Zone EFS for use cases like development environments or easily re-creatable data, whereas **Standard (Regional)** EFS should be used for production and business-critical workloads that need multi-AZ resilience.
  * **Performance Overhead for Small I/O:** EFS’s distributed architecture introduces a small latency (hundreds of microseconds to low milliseconds) for each file operation. For most use cases this overhead is negligible, but applications performing **a high volume of small, synchronous operations** (e.g., many small file writes in series) could experience latency-bound performance. Throughput on EFS scales with parallelism and request size, so such applications should be designed to use concurrent threads/requests or larger I/O batches to fully utilize EFS’s capacity. Workloads requiring extremely low latency per operation (e.g., high-frequency trading, certain database workloads) might be better served by local NVMe or Amazon EBS storage.
  * **Regional Service (No Native Multi-Region):** An EFS file system exists within a single AWS region and does not natively span regions. While you can manually set up **EFS Replication** to copy data to another region, this replication is asynchronous (typically within 1 minute, with an RPO of \~15 minutes) and requires a failover process. There is no automatic failover across regions. Thus, for **disaster recovery** or multi-region active-active scenarios, additional planning is required (e.g., using EFS Replication for DR, or using a combination of EFS and S3 for multi-region data distribution).

* **Additional Guardrails:** *TBD* – (Any additional internal guardrails or policies for using EFS, such as mandatory backup schedules, tagging requirements, etc., will be defined here.)

* **Used By:** *TBD* – (List of example projects or teams within the organization that are using EFS, with links or references to their implementations, to provide context and learning resources.)

* **EA Declaration:** *NOT* a declared standard. (Amazon EFS is currently not an officially declared standard service in PepsiCo’s Enterprise Architecture; usage may require case-by-case approval or exception until it is reviewed and approved by the EA committee.)

## Technical Guidelines

* **Best Practices:**

  * **Data Security:** Always enable encryption at rest when creating EFS file systems. This can be done with a checkbox in the console or parameter in IaC templates, and it uses AWS KMS-managed keys (you can use AWS-managed or customer-managed CMKs as required). In fact, AWS organizations can enforce this via IAM policy conditions (e.g., deny EFS creation if not encrypted). Additionally, mount EFS with encryption in transit by using the EFS mount helper (`amazon-efs-utils`), which can automatically set up TLS tunneling for NFS. This ensures that sensitive data classifications (Confidential, Restricted) are protected both at rest and in flight. Also, apply network-layer security – for example, place EFS mount targets in private subnets and security groups that only allow NFS traffic from known application servers.
  * **Performance & Throughput Optimization:** Use **General Purpose** performance mode for the vast majority of workloads. General Purpose mode provides the lowest per-operation latency and has been scaled to handle very high IOPS (up to 250k+ ops/sec as of recent updates) without needing the older “Max I/O” mode. (Max I/O mode, which trades off some latency for virtually unlimited IOPS, is generally only needed for extremely high parallelization requirements, and is not even supported on newer features like One Zone or Elastic throughput.) For throughput, start with **Elastic Throughput** (the default as of 2023) which automatically adjusts to your workload, and consider **Provisioned Throughput** only if you have a steady high throughput demand above the default. Design your applications to take advantage of parallelism – multiple threads or processes performing I/O – as EFS can deliver higher aggregate throughput with increased concurrency. Monitor the CloudWatch metric `PercentIOLimit` or throughput utilization graphs to know if you’re nearing EFS performance ceilings, and scale out or adjust accordingly.
  * **Cost Management:** Enable **Lifecycle Management/Intelligent-Tiering** on your file systems to automatically reduce cost for cold data. For instance, you might set a policy to move files to the Standard-IA or One Zone-IA tier if they haven’t been accessed for 30 days. This can dramatically cut storage costs (by \~85% for IA storage) for datasets with infrequent access patterns. Organize data so that temporary or less valuable data (like caches, logs, scratch files) can be periodically purged or moved to cheaper storage (such as archival in Amazon S3 or Glacier if appropriate). Also, use tagging on file systems (e.g., environment, project, cost center) and utilize AWS Cost Explorer or cost allocation reports to monitor EFS costs. This visibility can help ensure that large or unused file systems are reviewed regularly.
  * **Monitoring & Maintenance:** Treat EFS as part of your application’s critical infrastructure. Use **Amazon CloudWatch** to set up alarms on key metrics: for example, alarm on high burst credit consumption (if using Bursting throughput), high throughput utilization, or sudden increases in data usage (which could indicate an unexpected upload or data retention issue). Also monitor the **BurstCreditBalance** (for Bursting mode) and **ClientConnections** to ensure you stay within recommended limits. Enable **AWS CloudTrail** for EFS to log all API calls that create, modify, or delete file systems, mount targets, and access points. These logs can feed into security monitoring tools to detect any unauthorized changes. Regularly review your EFS file systems for unused data or stale file systems. Delete file systems that are no longer needed (after backing up data if necessary) to avoid accruing unnecessary costs.

* **High Availability & Disaster Recovery:**

  * **In-Region High Availability:** EFS (Standard) is inherently highly available within an AWS region. To maximize this, create a **mount target in each Availability Zone** where your compute resources run. By doing so, instances in any AZ can mount the EFS file system locally. If one AZ experiences issues, your application can fail over to instances in another AZ which are already using the same shared file system. The EFS file system itself will be unaffected by a single AZ outage (aside from the affected AZ’s mount target) because the data is redundantly stored across AZs. AWS notes that EFS Standard is designed such that “you can architect your application to failover from one AZ to others” seamlessly. One implementation detail: ensure your application or orchestration (e.g., auto-scaling group, Kubernetes) is configured to redistribute or restart workloads in healthy AZs if an AZ fails, and that those workloads mount the EFS file system at startup.
  * **Cross-Region Disaster Recovery:** If you have strict DR requirements (e.g., business continuity even in the rare event of a total regional outage), incorporate AWS’s **EFS Replication** feature. EFS Replication allows you to configure an automated, continuous replication of your file system to another AWS region. Most file changes are replicated within seconds, and the service targets an RPO (Recovery Point Objective) of 15 minutes or better for most workloads. In a disaster scenario, you can “promote” the replica to read-write by breaking replication, allowing your applications to switch to the replica file system in the DR region. This approach should be complemented by infrastructure-as-code or AMI strategies to re-deploy application servers in the DR region. **Important:** EFS replication is not instantaneous and does not guarantee an RPO of zero, so some recent data could be lost in a failover. For additional protection or finer recovery points, use **AWS Backup** to take nightly backups of EFS (or more frequently, as needed). AWS Backup allows point-in-time restores, which could be used in the same or a different region (AWS Backup supports cross-region backup copying). The best practice for critical data is to use both replication (for quick failover with minimal data loss) and backups (for point-in-time recovery, e.g., to recover from accidental deletions or corruption). Regularly test your DR plan by simulating failover to the replica and performing data integrity checks, as well as test restoring from backups, to ensure your procedures and automation work as expected.

* **Backup & Recovery:**

  * **Automated Backups with AWS Backup:** Leverage **AWS Backup** to protect your EFS file systems. AWS Backup can be set to take scheduled backups (snapshots) of EFS – for example, nightly or multiple times per day, depending on RPO requirements. These backups capture the state of the file system at that point in time. When you create a new EFS file system through the AWS Console, you have the option (enabled by default) to turn on automatic daily backups. It is a best practice to enable this unless you have an alternate backup strategy. Backups are stored in AWS Backup’s vault, and you can apply retention policies (e.g., keep daily backups for 30 days, monthlies for 1 year, etc.) according to your compliance needs. Ensure the backup plan and vault are configured to meet PepsiCo’s data retention standards (especially for Restricted data). Backups can also be **copied to a different region or account** for extra safety against region-wide issues or account-level compromises.
  * **Restore and File-Level Recovery:** In the event of data loss or corruption, you can **restore** an EFS backup to a new file system. This new file system can be in the original region or a different region (if you’ve copied the backup). The restore operation will recreate all files and directories as of the backup point. AWS Backup also supports **partial restore** for EFS, allowing you to restore individual files or directories from a backup without restoring the entire file system. This is extremely useful for scenarios like an accidental deletion of a single folder – you can initiate a restore of just that folder to an alternate location or a new file system, and then copy it back to the original. To use partial restore, you specify the path(s) to recover, and AWS Backup will assemble just those items in a new file system. When planning recovery, note that large restores (especially full file system recoveries in the tens of TBs) will take time to complete, proportional to the amount of data. EFS is not versioned, so if you need version-level recovery (e.g., revert a file to yesterday’s version), you must either restore from backup or rely on application-level versioning (like keeping previous copies, source control, etc.). It’s a good practice to document your restore processes and regularly exercise them (e.g., perform a test restore of a random file or do a full restore dry-run annually) so that you are confident in meeting your RTO (Recovery Time Objective).

* **Access Control & Security Configuration:**

  * **Network Access Control:** EFS uses mount targets in your VPC, each associated with a security group. Configure these security groups to allow NFS traffic (TCP port 2049) *only* from the specific clients or client groups that need access. For example, if you have an EFS serving an application tier, restrict the EFS security group to allow inbound 2049 from the security group of the EC2 instances or ECS tasks in that tier. Do not leave EFS open to all IPs or the entire VPC by default. Also consider using VPC subnets and routing to isolate EFS traffic; for instance, place EFS mount targets in subnets that are not directly reachable from the internet (no IGW route) to reduce exposure. **Tip:** Use AWS Security Groups’ ability to reference other groups by ID (inbound rule source = SG of your instances) – this way, only instances with that SG can access EFS, and as your auto-scaling adds/removes instances, the rules remain valid.
  * **Encryption & Key Management:** Always use encryption at rest for EFS (which is supported transparently). If you have specific regulatory needs, you might use a customer-managed CMK in KMS for EFS rather than the AWS-managed key. Ensure only authorized roles have access to that CMK. You can enforce encryption at rest by using an IAM condition on `elasticfilesystem:Encrypted` so that any attempt to create an unencrypted file system is denied. For encryption in transit, use the EFS mount helper with the `tls` option (e.g., `mount -t efs -o tls ...`). This wraps NFS traffic in TLS, satisfying requirements for sensitive data in transit. If using EFS Access Points, note that the POSIX user identity enforced by an access point does not encrypt or otherwise secure data – it’s for authorization, not encryption – so still apply transport encryption underneath.
  * **IAM Authorization & EFS Resource Policies:** EFS provides an option for **IAM authorization for NFS clients**, which, when enabled, means any NFS client must present AWS credentials (typically via a signed NFS mounting request using the EFS mount helper) to gain access. This adds an additional check on top of network security and POSIX permissions, effectively tying access to an IAM principal (like an EC2 IAM role). Use this feature for sensitive file systems or multi-tenant scenarios – for example, require that only EC2 instances with a certain IAM role can mount the file system. Complement this with an EFS **File System Policy** (a JSON IAM resource policy on the file system) that explicitly grants or denies access to certain principals. File system policies can enforce conditions such as requiring TLS for mounts or restricting access to certain AWS account IDs. They are especially useful in cross-account mounting scenarios (where producers and consumers might be in different AWS accounts within PepsiCo). Keep file system policies as simple as possible – grant necessary access to your application roles and deny everything else by default.
  * **Access Points & POSIX Permissions:** Utilize **EFS Access Points** to manage multi-application access to a single file system. An Access Point can enforce a specific user ID, group ID, and directory for the connecting application. For example, if two microservices share one EFS, you can create two Access Points, each chrooted to a different directory (say `/serviceA` and `/serviceB`) and each with its own POSIX identity (so service A always writes files as user “appA”, etc.). This separation ensures that even if the services accidentally interact, they are confined to their own areas and have their own permission sets. On the file system itself, continue to use standard **POSIX permissions** (owner, group, others with read/write/execute bits) to control access within each directory. Keep the file permission scheme straightforward (avoid overly permissive 777 modes; instead use appropriate user/group ownership). Note that **NFSv4 ACLs are not supported on EFS** – only the simple POSIX mode bits apply. If you try to set an NFS ACL from a client, it will be ignored. Therefore, plan your user/group strategy such that POSIX modes suffice. (E.g., you may leverage Unix groups to share data between certain applications and set group permissions accordingly.) Finally, ensure that the root directory of the EFS is not world-writable – it’s a good practice to restrict top-level directories and create subdirectories for each application or team, owned by the appropriate Unix user/group.
  * **Monitoring & Auditing:** EFS itself does not generate file access audit logs (i.e., there is no AWS service that logs each file open or read on EFS). Therefore, if file-level audit is required, you might need to enable OS-level logging on your instances (for example, using Linux’s auditd to record file access events). However, at the service level, you should enable **CloudTrail** for Amazon EFS, which will log operations like CreateFileSystem, DeleteFileSystem, CreateAccessPoint, etc.. These logs are crucial to detect any unauthorized changes to your EFS configurations. Consider integrating CloudTrail with CloudWatch Logs and setting up alerts or using AWS CloudWatch Events (EventBridge) to trigger notifications on critical EFS events (like someone deleting a file system or changing a resource policy). Additionally, monitor CloudWatch metrics for any signs of misuse – for instance, a sudden spike in the `ClientConnections` metric might indicate an unintended exposure or mounting by many clients. If using IAM authorization, monitor the CloudWatch metric for `AccessDenied` client mount attempts (available via the EFS mount helper logs or CloudWatch if configured) to catch any unauthorized mount attempts. Regularly audit who (which IAM roles/principals) has permission to create or modify EFS file systems, and apply the principle of least privilege on those IAM policies.

* **Network Connectivity Options:**

  * **Public Internet Access:** **Not Allowed.** Amazon EFS has no public endpoints and cannot be accessed directly from the internet. All access to EFS must occur over an internal network path. Typically, this means via your VPC or via an AWS Direct Connect/VPN into your VPC. Even when you use AWS Transfer Family for SFTP (an AWS managed service that provides a publicly reachable endpoint), that service internally writes to EFS – your EFS itself still isn’t exposed publicly. In summary, clients must either be in the AWS environment or connect through secure network channels; you should *never* attempt to make EFS accessible via a public IP. (Any such configuration would involve exposing an EC2 proxy, which is not permitted for sensitive data.)
  * **VPC Internal Access:** **Allowed (Default).** Amazon EFS is designed for VPC connectivity. When you mount EFS from an EC2 instance in the same VPC, traffic stays within the AWS private network. Mount targets provide a local IP in each subnet/AZ for clients to connect to. This is the standard configuration: as long as your EC2 (or ECS/EKS) resources share a VPC (or have network connectivity via peering/TGW) with the EFS mount target, they can mount the file system. This option is allowable for all data classifications (Public, Internal, Confidential, Restricted) because it does not traverse the internet. Ensure that the security group rules are in place as discussed above. If you need cross-VPC access (e.g., EFS in a central VPC, accessed by instances in another VPC), you can use VPC **Peering** or **Transit Gateway** to enable that connectivity. EFS mount targets can be accessed across peered VPCs or via a Transit Gateway, as long as network ACLs and security groups permit it. One limitation: you cannot mount an EFS file system over VPC peering if the peered VPC is in a different AWS region (inter-region peering), unless you use a workaround like an EC2 proxy or the new EFS Cross-Region Data Access via AWS Datasync or replication. Typically, keep the EFS in the same region as the clients for performance and simplicity.
  * **On-Premises (VPN/Direct Connect):** **Allowed (with secure connectivity).** You can mount EFS from on-premises servers by establishing a **Site-to-Site VPN** or **AWS Direct Connect** connection into your VPC. This effectively extends your corporate network into AWS. The on-prem server will resolve the EFS mount target DNS name (which ends in `.amazonaws.com`) to a private IP in the VPC, and then it will send NFS traffic over the VPN/DX to that IP. This setup is supported and can be very useful for hybrid workflows (for example, a local data center server processing files stored in EFS, or using EFS as a target for on-prem backups). Be mindful of network latency – NFS over long distances can have high latency, which affects throughput for synchronous operations. To mitigate this, ensure you have sufficient bandwidth (DX is preferred for large workloads) and consider using AWS DataSync which is optimized for transferring files over WAN. For data classifications up to Confidential and Restricted, this approach is allowed as long as the network connection is secure (VPN with strong encryption or private dedicated link via Direct Connect) and the on-prem environment is within PepsiCo’s controlled network. Treat the on-prem server as an extension of the cloud security boundary when it mounts EFS.
  * **Interface VPC Endpoints (AWS PrivateLink):** **Allowed (Recommended for API calls).** AWS offers an **Interface Endpoint** for Amazon EFS, which enables you to make EFS API calls (create, modify, delete file systems, etc.) from within your VPC without going through the public AWS API endpoints. This is particularly useful for automation or services that manage EFS (like CloudFormation or Terraform running in a private subnet) – they can use the EFS API internally. However, note that the **data plane** (mounting the file system via NFS) does not use the interface endpoint; it goes directly to mount target IPs. For most users, enabling the EFS interface endpoint is a good practice to avoid any dependency on internet connectivity for EFS management operations, especially in high-security environments. It’s a simple setup via the AWS Console or CLI (create VPC Endpoint for com.amazonaws.<region>.elasticfilesystem). Ensure your endpoint security group (if any) allows relevant traffic and that DNS resolution for the EFS API is enabled in the VPC.
  * **AWS Transfer Family:** **Conditionally Allowed (for specific use cases).** AWS Transfer Family can provide SFTP, FTPS, or FTP access to an EFS file system for external users or systems. This managed service creates a publicly accessible endpoint (or it can be put behind a VPC endpoint as well) that terminates SFTP/FTPS/FTP and then reads/writes to EFS on your behalf. This is useful if you need to exchange files with third parties securely using standard protocols. Within PepsiCo, this should be used only when necessary and with proper controls: you would typically create specific IAM users for Transfer Family or integrate with an identity provider, limit their access to specific directories (possibly via EFS Access Points), and enforce cryptographic protocols (SFTP or FTPS, not plain FTP for any sensitive data). The Transfer Family service runs in your VPC, so it will mount the EFS using an ENI – ensure the Transfer Family’s role has permission to mount the EFS (via an EFS resource policy if cross-account, etc.). Because this does expose data externally, classify the data appropriately and ensure compliance (e.g., no Restricted data should be shared via FTP interfaces without heavy scrutiny). Transfer Family logs user activity to CloudWatch Logs, which you should monitor. In summary, Transfer Family is allowed but should be treated as an *exception* and require additional approval for use with sensitive data.

* **Networking & Security Architecture:** *TBD* – (Detailed reference architecture diagrams and network topology for EFS deployments will be provided here. This may include diagrams showing EFS in a shared services VPC, mount target placement, security group schema, and data flows between application tiers and EFS, as well as any additional security layers such as firewall appliances or intrusion detection specific to file traffic.)

* **AWS Service Limitations:**

  * **Operating System Support:** EFS is not a universal file system for all OSes. **Windows clients are not supported** for EFS mounts, as EFS relies on NFSv4, which Windows does not natively fully support (Windows has some NFS client capability but with limitations, and AWS does not guarantee compatibility). Additionally, older Linux kernels must support NFSv4; most modern distributions do. Ensure your client instances use an OS with NFSv4.0 or 4.1 support (Amazon Linux, RHEL, Ubuntu, etc., all support this out of the box). If Windows access is required, consider AWS FSx for Windows (SMB protocol) or setting up a file gateway.
  * **File System Size and Object Limits:** An EFS file system can theoretically grow to petabytes, and AWS reports no fixed upper limit on total storage. However, individual **files** have a maximum size of *47.9 TiB* (52,673,613,135,872 bytes). This is extremely large (likely far beyond typical use), but it could be a factor for certain big data or HPC applications that create single huge files (e.g., giant tar archives or database backups). Also, each directory can contain a very large number of files, but performance may degrade if you have *millions of files in a single directory*. The **maximum directory depth** is 1,000 levels, which is usually not a practical limitation (going that deep is rare). There is a limit of 255 bytes for a file or directory name. EFS does not impose a limit on the number of files, but other factors (like inode count on the client or performance metadata limits) mean you should be cautious when approaching tens of millions of files – ensure your application is tuned for that scenario.
  * **Connections and Throughput Limits:** **Scalability** of EFS is high, but not infinite. Each EFS file system can handle up to 25,000 concurrent NFS client connections (mounts). This is usually more than enough (e.g., even if each EC2 handles 4 mounts, that’s thousands of instances), but extremely large HPC clusters or multi-account setups should mind this. In terms of operations, in General Purpose mode, the file system can process roughly 250,000 operations per second (metadata or I/O ops) before hitting a ceiling (the **PercentIOLimit** metric would reach 100%). This limit has increased over time (it used to be much lower in the past), and AWS can raise it with a case if needed, but at that point one might consider sharding data across multiple EFS file systems for horizontal scale. Per-client, an EC2 instance can drive up to 500 MiB/s of throughput (by default) and up to 65,536 file locks and files open concurrently (these are NFS client kernel limits). If you need to exceed these per-client throughput limits (for example, a single instance processing at higher rate), AWS provides an *optimized EFS mount helper* (using NFS over multiple connections) and Elastic throughput mode to go beyond 500 MiB/s (up to 1,500 MiB/s with certain configurations). Such use cases are rare. Most performance or scaling limitations can be addressed by either increasing concurrency (more clients) or contacting AWS for guidance.
  * **NFS Protocol Limitations:** Amazon EFS currently supports **NFSv4.0 and NFSv4.1** only. It does **not support NFSv3 or earlier**. Additionally, certain NFSv4 features are not implemented. For example, **NFSv4 ACLs (Access Control Lists)** are not supported – EFS will not enforce them even if a client sets them. Only the traditional POSIX permissions are effective. **Mandatory locking** is not supported (NFS locks are advisory on EFS, meaning a lock will prevent other clients from acquiring a lock, but not from I/O if they choose to ignore locks). **POSIX byte-range locks** are supported in an advisory manner (and the limit of locks is 512 per file across all clients). **Parallel NFS (pNFS)** is not supported; EFS itself is a distributed system under the hood, but it presents a single mount point without pNFS striping to clients. Also, some low-level NFS operations or flags that are seldom used may not be available (e.g., NFS delegations). Practically, for most users these NFS limitations mean you should design around standard file locking conventions and not attempt to use extended ACLs for permissions (use IAM and access points instead if needed). If your application requires features like Windows-style file locking or rich ACLs, EFS might not be a fit – consider FSx for Windows (for SMB + Windows ACL) or FSx for NetApp ONTAP (which supports NTFS ACLs and some advanced NAS features).
  * **No Native Snapshotting or WORM:** Unlike some on-prem NAS appliances, EFS does not yet offer native snapshot or WORM (write-once-read-many) capabilities to end-users. Snapshots are achieved via AWS Backup, as discussed, and there is no instant revert feature except to restore from backup. If your use case requires user-driven snapshots (e.g., users taking a snapshot of their home directory before making changes) or legal holds (WORM), you would need to implement those through external mechanisms (like versioning in the application layer or using AWS Backup’s Vault Lock for compliance). AWS Backup’s integration partially fills this gap but is not instantaneous. Keep this in mind for data protection strategies. On a related note, **data consistency** on EFS is *strong* (read-after-write for all clients) which is good, but there is no multi-site consistency (as it’s single-region). Also, **Soft Delete**: if a file is deleted on EFS, it’s immediately gone – there’s no recycle bin. This underscores the importance of backups and IAM controls to prevent accidental deletes.

* **SKU Features:** *(Amazon EFS does not have “SKUs” in the same sense as some cloud services; instead, it offers different storage class options. Below is a summary of each storage class and its characteristics.)*

  * **EFS Standard (Regional):**

    * Multi-AZ storage – data is redundantly stored across multiple Availability Zones in the region, providing resilience to AZ failures. This is the recommended default for production.
    * High durability and availability – designed for 99.999999999% durability and an SLA of 99.99% availability (when mounted in two AZs). It can transparently survive an AZ outage without data loss.
    * Performance optimized – uses SSD-based storage for active data. Offers low latency (typically sub-millisecond for reads, single-digit milliseconds for writes) for most operations. Suitable for latency-sensitive and high-throughput workloads.
    * Supports all EFS features – can use Elastic or Provisioned throughput, supports Access Points, IAM auth, etc. Also allows enabling lifecycle policies to transition data to Standard-IA.
    * Pricing: Storage in Standard is at the higher tier price (since it’s triple-replicated across AZs). Best used for frequently accessed, mission-critical data.
  * **EFS Standard-Infrequent Access (Standard-IA):**

    * Infrequent Access tier for Regional file systems – data is still stored across multiple AZs (with the same durability), but priced lower for storage and with an additional charge per read/write access.
    * Higher latency – retrieval from IA may incur *tens of milliseconds* of latency (due to the storage media optimized for infrequent access). This is fine for cold data but not ideal for real-time access.
    * Lifecycle-managed – you typically don’t put data directly into Standard-IA; instead, you enable a lifecycle policy (e.g., 30 days since last access) and EFS will automatically move files to IA. When those files are accessed again, they incur a throughput/data retrieval charge.
    * Use cases: Great for long-lived data that is rarely read (archives, old project data, compliance logs that must be retained, etc.) that still requires high durability. Because it retains multi-AZ resilience, it’s safe for even critical data that just isn’t accessed often.
    * Pricing: \~\$0.025/GB-month (example) and a per-GB fee for data read from IA. It’s roughly 4x cheaper than Standard storage. Using IA can dramatically reduce costs for the portions of your dataset that are cold.
  * **EFS One Zone:**

    * Single-AZ storage – data is stored redundantly within **one** AZ (multiple servers in that AZ, but not outside it). If that AZ has an outage or loss, the file system in One Zone will be unavailable or could be lost.
    * Lower cost – storage pricing is about 47% lower than Standard storage (since it foregoes cross-AZ replication). This makes it very attractive for non-critical, reproducible, or backup data.
    * High performance – One Zone uses the same SSD storage for active data, so performance and latency are comparable to Standard (in fact, slightly lower write latencies have been observed because data doesn’t traverse AZs). It supports General Purpose performance mode (Max I/O not available for One Zone) and all throughput modes. The per-AZ throughput limits are the same as a single AZ of Standard (5 GiB/s read, 3 GiB/s write bursting in us-east-1, for example).
    * Feature support – supports Access Points, IAM auth, etc., just like Standard. Also supports its own IA tier (One Zone-IA). When you enable lifecycle policies on a One Zone FS, files move to One Zone-IA after the set time.
    * Use cases: Ideal for development and test environments, where you need shared storage but can tolerate downtime or data loss in a worst case. Also used for analytics scratch space, caches, or intermediate data that is either ephemeral or backed by another source. Some customers use One Zone for replicating on-prem data (like as a secondary copy) where the primary copy is elsewhere – in such cases, One Zone provides a cheaper cloud storage for that replica. Given PepsiCo’s data classifications, One Zone might be acceptable for Internal data or lower, or for specific Confidential data sets that are non-critical (with approval). Restricted data likely should stay on multi-AZ unless specifically excepted.
  * **EFS One Zone-Infrequent Access (One Zone-IA):**

    * Infrequent Access tier for One Zone file systems. Like Standard-IA, it offers lower-cost storage for data that is rarely accessed, but within a single AZ.
    * Durability/Availability: Because it’s single AZ, data is at risk if that AZ fails. You would typically use One Zone-IA for data that is either duplicated elsewhere or not mission-critical. AWS still replicates data within that AZ, so device failures are handled, but an AZ outage could make data unavailable or result in loss.
    * Latency and performance: Similar pattern to Standard-IA – slightly higher access latency (tens of milliseconds) and a cost per GB retrieved. If an One Zone file system is mostly IA, initial access after a period of cold storage may see a spike in latency while data is fetched from the IA storage tier.
    * Use cases: Archival data, backups, or datasets used for occasional analysis, where low cost is prioritized over high availability. For example, if you have a development environment EFS with build artifacts, and older artifacts are rarely needed, One Zone-IA could store those cheaply. However, if the AZ went down, those artifacts might need to be rebuilt. It’s best used when you can tolerate reconstituting or losing that data.
    * Pricing: lowest cost among EFS tiers – roughly 1/8th the cost of Standard storage per GB (exact numbers vary by region). This is comparable to Amazon S3 Standard-IA pricing, but note EFS One Zone-IA is file storage with different access patterns.
    * Management: Enabled via lifecycle policies on One Zone FS (just like Standard). You cannot directly create a One Zone-IA file system; it’s always an offshoot of a One Zone file system with lifecycle rules. Accessing One Zone-IA data will incur additional fees, so monitor usage if you see unexpected charges (it might indicate someone or something is scanning through archive data frequently, which could warrant moving it back to standard storage).

* **Related Service:** *TBD* – (This section will list services related or comparable to EFS for context. For example: **Amazon FSx for Windows File Server** for SMB-compatible shared storage in AWS, **Amazon FSx for NetApp ONTAP** for advanced NAS features and multi-protocol support, **Amazon S3** as an alternative for object storage of files (with different consistency/semantics), or **AWS Storage Gateway** for extending file storage to on-prem. These related services can be used in scenarios where EFS may not be the perfect fit, such as needing Windows ACLs or global access. Since this is TBD, details will be filled in by the service architect.)

## Compliance and Security Guidelines

* **Security Baseline InfoSec:**

  * **Information Security Specifications – Amazon EFS:** This service must comply with PepsiCo’s cloud security baseline for file storage. Key points include enforcing encryption (at rest and in transit), strict access controls (network isolation and IAM authorization), regular audit of permissions, and data classification compliance (ensuring that data stored in EFS meets the handling requirements of its classification). All **Restricted** data stored on EFS must be encrypted with company-managed keys and accessible only on private networks. The **AWS CIS Benchmark** and PepsiCo’s internal cloud security standards for storage services should be applied. Logging via CloudTrail should be enabled and integrated with PepsiCo’s SIEM for monitoring. Additionally, EFS usage may need to adhere to **GDPR** or other regulatory frameworks if it stores personal data – meaning access logging and data lifecycle (right-to-delete) processes might need to be implemented at the application level. (Detailed InfoSec baseline documentation is to be referenced here, covering EFS-specific controls.)

## Ownership and Version Control

* **Service Architect:** Dariusz Korzun ([<EMAIL>](mailto:<EMAIL>))
* **Version Control:**

  * *v.1:* 22 Jul 2025 – Document created (Dariusz Korzun)